# FTP服务器访问说明

## 📋 概述

本项目提供了两个Python脚本来访问您的FTP服务器：

1. **`ftp_access.py`** - 完整的FTP客户端类，适合集成到其他项目中
2. **`ftp_simple_example.py`** - 简化的交互式版本，适合快速操作

## 🔑 FTP账号信息

- **账号**: `xgm-wl`
- **密码**: `HscmiT4Cyhzp2PaM`
- **目标路径**: `/wwwroot/透明计划`

## 🚀 快速开始

### 使用简化版本（推荐新手）

```bash
python ftp_simple_example.py
```

运行后按照提示输入FTP服务器地址即可开始使用。

### 使用完整版本

```bash
python ftp_access.py
```

需要先修改脚本中的 `FTP_HOST` 变量为实际的FTP服务器地址。

## 📖 详细使用方法

### 1. 基本连接代码示例

```python
import ftplib

# 连接配置
FTP_HOST = "your-ftp-server.com"  # 替换为实际服务器地址
FTP_USERNAME = "xgm-wl"
FTP_PASSWORD = "HscmiT4Cyhzp2PaM"
TARGET_PATH = "/wwwroot/透明计划"

# 创建连接
ftp = ftplib.FTP()
ftp.connect(FTP_HOST, 21)
ftp.login(FTP_USERNAME, FTP_PASSWORD)
ftp.encoding = 'utf-8'  # 支持中文文件名

# 切换到目标目录
ftp.cwd(TARGET_PATH)

# 列出目录内容
ftp.retrlines('LIST')

# 关闭连接
ftp.quit()
```

### 2. 常用操作

#### 📂 列出目录内容
```python
ftp.retrlines('LIST')  # 详细列表
# 或者
files = []
ftp.retrlines('LIST', files.append)  # 保存到列表
```

#### 📁 切换目录
```python
ftp.cwd('/wwwroot/透明计划')  # 切换到指定目录
current_path = ftp.pwd()      # 获取当前路径
```

#### ⬇️ 下载文件
```python
# 下载文件
with open('local_file.txt', 'wb') as f:
    ftp.retrbinary('RETR remote_file.txt', f.write)
```

#### ⬆️ 上传文件
```python
# 上传文件
with open('local_file.txt', 'rb') as f:
    ftp.storbinary('STOR remote_file.txt', f)
```

#### 🗂️ 创建目录
```python
ftp.mkd('新文件夹')  # 创建目录
```

#### 🗑️ 删除文件
```python
ftp.delete('filename.txt')  # 删除文件
```

### 3. 使用FTPClient类（推荐）

```python
from ftp_access import FTPClient

# 创建客户端
client = FTPClient("your-ftp-server.com", "xgm-wl", "HscmiT4Cyhzp2PaM")

# 连接并操作
if client.connect():
    client.change_directory("/wwwroot/透明计划")
    client.list_directory()
    client.download_file("remote_file.txt", "local_file.txt")
    client.upload_file("local_file.txt", "new_remote_file.txt")
    client.disconnect()
```

## ⚠️ 注意事项

1. **服务器地址**: 您需要提供正确的FTP服务器IP地址或域名
2. **路径格式**: FTP服务器路径通常以 `/` 开头（Unix风格）
3. **中文支持**: 脚本已设置UTF-8编码支持中文文件名
4. **防火墙**: 确保FTP端口（默认21）没有被防火墙阻止
5. **网络连接**: 确保网络连接正常

## 🛠️ 故障排除

### 常见错误及解决方案

1. **连接超时**
   - 检查FTP服务器地址是否正确
   - 检查网络连接
   - 确认防火墙设置

2. **登录失败**
   - 验证用户名和密码
   - 确认账号是否有效

3. **目录不存在**
   - 检查路径是否正确
   - 确认是否有访问权限

4. **中文文件名乱码**
   - 脚本已设置UTF-8编码，如仍有问题请检查服务器编码设置

## 📝 示例：批量操作

```python
from ftp_access import FTPClient
import os

def batch_upload_files(local_dir, remote_dir):
    """批量上传文件夹中的所有文件"""
    client = FTPClient("your-ftp-server.com", "xgm-wl", "HscmiT4Cyhzp2PaM")
    
    if client.connect():
        client.change_directory(remote_dir)
        
        for filename in os.listdir(local_dir):
            local_path = os.path.join(local_dir, filename)
            if os.path.isfile(local_path):
                print(f"正在上传: {filename}")
                client.upload_file(local_path, filename)
        
        client.disconnect()

# 使用示例
batch_upload_files("./local_folder", "/wwwroot/透明计划")
```

## 🔧 环境要求

- Python 3.6+
- 内置模块：`ftplib`, `os`, `sys`
- 无需额外安装依赖包

## 📞 技术支持

如果遇到问题，请检查：
1. FTP服务器地址是否正确
2. 网络连接是否正常
3. 账号密码是否正确
4. 目标路径是否存在 