{"query": "query ($aggregatedInput: Filter!,$daywiseInput:Filter!) {\n        aggregated:get_auroraAnalyticsService_overview (input: $aggregatedInput){\n          overviewRecords {\n            baseMetrics {\n              date\n              cancelledGMV\n              refundedGMV\n              sku\n              subCategory\n              totalAuthUnits\n              totalAuthOrders\n              totalAuthAmount\n              totalGMV\n              totalUnits\n              avgUnitRetail\n              totalGMVWithoutCommission\n              totalPriorGMVWithoutCommission\n              cancelledUnits\n              refundedUnits\n              totalPriorUnits\n            }\n            metricsChange {\n              gmvPercentageChange\n              gmvWithoutCommissionPercentageChange\n              unitSoldPercentageChange\n              avgUnitRetailPercentageChange\n              ordersPercentageChange\n            }\n            compareMetrics {\n              totalGMV\n              totalUnits\n              totalAuthOrders\n              avgUnitRetail\n            }\n          }\n        }\n        daywise:get_auroraAnalyticsService_overview (input: $daywiseInput) {\n            overviewRecords {\n              baseMetrics {\n                date\n                cancelledGMV\n                refundedGMV\n                sku\n                subCategory\n                totalAuthUnits\n                totalAuthOrders\n                totalAuthAmount\n                totalGMV\n                totalUnits\n                avgUnitRetail\n                totalGMVWithoutCommission\n                totalPriorGMVWithoutCommission\n                cancelledUnits\n                refundedUnits\n                totalPriorUnits\n              }\n              metricsChange {\n                gmvPercentageChange\n              }\n              compareMetrics {\n                date\n                totalGMV\n                totalUnits\n                totalAuthOrders\n                avgUnitRetail\n              }\n            }\n          }\n      }", "variables": {"aggregatedInput": {"filters": {"comparisonEnabled": true, "dateFilter": {"startDate": {"eq": "2025-05-01"}, "endDate": {"eq": "2025-05-31"}}, "search": [], "comparisonDateFilter": {"startDate": {"eq": "2024-05-01"}, "endDate": {"eq": "2024-05-31"}}, "program": {"eq": "ALL"}, "l1_department": [{"eq": "07 TOYS"}]}, "aggBy": "OVERALL"}, "daywiseInput": {"filters": {"comparisonEnabled": true, "dateFilter": {"startDate": {"eq": "2025-05-01"}, "endDate": {"eq": "2025-05-31"}}, "search": [], "comparisonDateFilter": {"startDate": {"eq": "2024-05-01"}, "endDate": {"eq": "2024-05-31"}}, "program": {"eq": "ALL"}, "l1_department": [{"eq": "07 TOYS"}]}, "aggBy": "DAY"}}}