# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/13 11:12
@Auth ： Tianshilin
@File ：PDF分割.py
@IDE ：PyCharm
"""
import os
from pathlib import Path
import fitz  # PyMuPDF
from PIL import Image
import io
import re
import sys
import logging
import datetime
import uuid
import base64
import time
import uuid
import base64
import time


def generate_short_uuid():
    # 获取当前时间戳（秒级）
    timestamp = int(time.time())

    # 生成一个随机 UUID4 并取其字节
    random_bytes = uuid.uuid4().bytes

    # 将时间戳转换为 4 字节（32位）
    timestamp_bytes = timestamp.to_bytes(4, byteorder='big')

    # 合并时间戳和随机字节
    combined_bytes = timestamp_bytes + random_bytes

    # 使用 Base64 编码并去除填充 '='
    short_uuid = base64.urlsafe_b64encode(combined_bytes).rstrip(b'=').decode('ascii')

    # 截取前 8-12 个字符（可根据需求调整）
    return short_uuid[:12]


def get_application_path():
    """获取应用程序路径，兼容PyInstaller打包后的环境"""
    if getattr(sys, 'frozen', False):
        # 如果是PyInstaller打包后的环境
        return os.path.dirname(sys.executable)
    else:
        # 如果是正常Python环境
        return os.path.dirname(os.path.abspath(__file__))


# 配置日志记录
def setup_logger():
    """设置日志记录器"""
    # 创建logs目录（如果不存在）
    log_dir = os.path.join(get_application_path(), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 生成日志文件名，包含日期
    log_date = datetime.datetime.now().strftime("%Y%m%d")
    log_file = os.path.join(log_dir, f"pdf_splitter_{log_date}.log")

    # 配置日志记录器
    logger = logging.getLogger("PDFSplitter")
    logger.setLevel(logging.INFO)

    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding="utf-8")
    file_handler.setLevel(logging.INFO)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 设置日志格式
    log_format = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    file_handler.setFormatter(log_format)
    console_handler.setFormatter(log_format)

    # 添加处理器到记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


# 全局日志记录器
logger = setup_logger()


def extract_tracking_number(filename):
    """从文件名中提取跟踪号码"""
    # 使用正则表达式提取数字部分，这里假设最后的数字部分是跟踪号
    match = re.search(r'(\d+)\.pdf$', filename)
    if match:
        return match.group(1)
    return "output"  # 如果没有找到匹配的数字，返回默认名称


def pdf_to_images(pdf_path, output_format="jpg", save_path=None):
    """
    将PDF文件转换为图片并保存到指定目录

    参数:
        pdf_path: PDF文件路径
        output_format: 输出图片格式，默认为jpg
        save_path: 指定保存路径，如果为None则使用当前目录
    返回:
        bool: 处理成功返回True，否则返回False
    """
    try:
        # 获取PDF文件名（不含扩展名）
        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]

        # 确定保存路径
        if save_path is None:
            # 如果没有指定保存路径，使用当前目录下的跟踪号命名目录
            tracking_number = extract_tracking_number(os.path.basename(pdf_path))
            base_path = get_application_path()
            output_dir = os.path.join(base_path, tracking_number)
        else:
            # 使用指定的保存路径，不再创建子目录
            output_dir = save_path

        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")

        # 获取PDF的完整路径
        if not os.path.isabs(pdf_path):
            pdf_path = os.path.join(get_application_path(), pdf_path)

        # 打开PDF文件
        logger.info(f"开始处理PDF: {pdf_path}")
        pdf_document = fitz.open(pdf_path)

        # 遍历PDF的每一页
        for page_num in range(len(pdf_document)):
            # 获取页面
            page = pdf_document[page_num]

            # 将页面渲染为图片
            pix = page.get_pixmap(matrix=fitz.Matrix(300 / 72, 300 / 72))  # 300 DPI

            # 将pixmap转换为PIL图像
            img_data = pix.tobytes("ppm")
            img = Image.open(io.BytesIO(img_data))

            # 保存图片
            output_filename = f"{output_dir}/{page_num + 1}.{output_format}"
            img.save(output_filename, quality=95)
            logger.info(f"已保存: {output_filename}")

        pdf_document.close()
        logger.info(f"PDF '{pdf_path}' 已成功转换为图片并保存到 '{output_dir}' 目录")
        return True
    except Exception as e:
        logger.error(f"处理PDF '{pdf_path}' 时出错: {str(e)}", exc_info=True)
        return False


def process_all_pdfs():
    """处理当前目录下及所有子文件夹中的PDF文件，并在处理完成后删除它们"""
    # 获取当前应用程序路径
    current_path = get_application_path()

    # 递归查找当前目录及所有子目录下的PDF文件
    pdf_files = []
    for root, dirs, files in os.walk(current_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))

    if not pdf_files:
        logger.warning("当前目录及子目录下没有找到PDF文件")
        return

    logger.info(f"在当前目录及子目录中找到 {len(pdf_files)} 个PDF文件待处理")

    # 处理每个PDF文件
    for pdf_file in pdf_files:
        logger.info(f"开始处理: {pdf_file}")
        file_path_data = Path(pdf_file)
        file_name = file_path_data.stem
        uuid_name = file_name+"-"+generate_short_uuid()
        data_path = f"C:\\wwwroot\\透明计划\\{file_name}\\{uuid_name}\\"
        success = pdf_to_images(pdf_file, save_path=str(data_path))
        # 如果处理成功，删除PDF文件
        if success:
            try:
                os.remove(pdf_file)
                logger.info(f"已删除: {pdf_file}")
            except Exception as e:
                logger.error(f"删除文件 '{pdf_file}' 时出错: {str(e)}")
        else:
            logger.warning(f"处理失败，保留文件: {pdf_file}")


if __name__ == "__main__":
    logger.info("=== PDF分割程序启动 ===")
    # 处理当前目录下所有PDF文件
    process_all_pdfs()
    logger.info("=== PDF分割程序完成 ===")
