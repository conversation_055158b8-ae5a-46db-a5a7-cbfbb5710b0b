@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ==========================================
echo 无忧面单数据处理 - Windows Server启动器
echo ==========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要管理员权限运行此程序
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [信息] 管理员权限检查通过
echo.

:: 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo [信息] Python环境检查通过
echo.

:: 检查必要的Python包
echo [信息] 检查Python依赖包...
python -c "import requests, pymysql, pathlib" >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 缺少必要的Python包，正在安装...
    pip install requests pymysql pathlib2
)

:: 创建必要的目录
if not exist "logs" mkdir logs
if not exist "output" mkdir output

echo [信息] 目录结构检查完成
echo.

:: 检查网络连接
echo [信息] 检查网络连接...
ping -n 1 ************* >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 无法连接到数据库服务器，请检查网络连接
) else (
    echo [信息] 数据库服务器连接正常
)

:: 检查NAS网络路径
echo [信息] 检查NAS网络路径访问权限...
if exist "\\segmart-nas\物流相关资料-公开" (
    echo [信息] NAS路径访问正常
) else (
    echo [警告] 无法访问NAS路径，请检查网络映射和权限
    echo [提示] 可能需要手动映射网络驱动器
)

echo.
echo ==========================================
echo 环境检查完成，正在启动程序...
echo ==========================================
echo.

:: 启动Python程序
if exist "请求获取面单数据_第五版.exe" (
    echo [信息] 运行EXE版本
    "请求获取面单数据_第五版.exe"
) else if exist "请求获取面单数据_第五版.py" (
    echo [信息] 运行Python脚本版本
    python "请求获取面单数据_第五版.py"
) else (
    echo [错误] 未找到程序文件
    pause
    exit /b 1
)

echo.
echo [信息] 程序已结束
pause 