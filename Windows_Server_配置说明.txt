Windows Server 2022 部署配置说明
=====================================

主要修改内容：
=============

1. 日志系统改进
   - 添加权限检查，避免文件创建失败
   - 支持 EXE 打包后的路径自动识别
   - 当无法创建文件日志时，自动降级为控制台日志

2. 网络路径访问优化
   - 添加 30 秒路径检查超时，避免长时间阻塞
   - 添加 60 秒目录遍历超时
   - 使用独立线程避免主程序卡死

3. 数据库连接改进
   - 添加连接、读取、写入超时设置（30秒）
   - 实现自动重试机制（最多3次）
   - 改进异常处理和连接管理

4. 信号处理兼容性
   - Windows 环境下只注册支持的信号
   - 添加 SIGBREAK 信号支持（Ctrl+Break）

5. 调度器稳定性增强
   - 连续错误计数机制
   - 错误达到阈值时延长等待时间到5分钟
   - 详细的错误日志记录

部署步骤：
=========

1. 环境准备
   - 以管理员身份运行
   - 确保 Python 环境正常（如果使用脚本版本）

2. 网络配置
   - 映射 NAS 网络路径：\\segmart-nas\物流相关资料-公开
   - 检查数据库服务器连接：120.25.103.16:3306

3. 运行程序
   - 推荐使用 Windows_Server_运行配置.bat
   - 或直接运行 exe/py 文件

常见问题解决：
============

1. 程序卡住不动
   - 检查网络路径访问权限
   - 查看 logs/error.log 文件
   - 确认以管理员身份运行

2. 数据库连接失败
   - 测试网络连接：ping 120.25.103.16
   - 检查防火墙设置
   - 查看错误日志详细信息

3. 文件权限错误
   - 确保以管理员身份运行
   - 检查目录写入权限
   - 手动创建 logs 和 output 目录

4. NAS 路径无法访问
   - 手动映射网络驱动器
   - 检查网络连接：ping segmart-nas
   - 确认用户有访问权限

监控要点：
=========
- 查看 logs/scheduler.log 了解运行状态
- 查看 logs/error.log 了解错误信息
- 程序每2分钟执行一次任务
- 连续错误5次后会延长等待时间

技术支持：
=========
如果问题仍然存在，请提供：
1. 详细的错误日志（logs/error.log）
2. 运行环境信息（Windows版本、网络配置）
3. 问题发生的具体步骤 