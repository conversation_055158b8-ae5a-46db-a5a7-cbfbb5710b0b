(['C:\\Users\\<USER>\\Desktop\\公司脚本\\无忧的面单\\请求获取面单数据_第五版.py'],
 ['C:\\Users\\<USER>\\Desktop\\公司脚本\\无忧的面单'],
 ['apscheduler',
  'apscheduler.schedulers.blocking',
  'apscheduler.schedulers',
  'apscheduler.triggers',
  'apscheduler.executors',
  'apscheduler.jobstores',
  'apscheduler.events',
  'requests',
  'pymysql',
  'pymysql.cursors',
  'tzlocal',
  'pytz',
  'six',
  'requests',
  'requests.__version__',
  'requests._internal_utils',
  'requests.adapters',
  'requests.api',
  'requests.auth',
  'requests.certs',
  'requests.compat',
  'requests.cookies',
  'requests.exceptions',
  'requests.help',
  'requests.hooks',
  'requests.models',
  'requests.packages',
  'requests.sessions',
  'requests.status_codes',
  'requests.structures',
  'requests.utils',
  'pymysql',
  'pymysql._auth',
  'pymysql.charset',
  'pymysql.connections',
  'pymysql.constants',
  'pymysql.constants.CLIENT',
  'pymysql.constants.COMMAND',
  'pymysql.constants.CR',
  'pymysql.constants.ER',
  'pymysql.constants.FIELD_TYPE',
  'pymysql.constants.FLAG',
  'pymysql.constants.SERVER_STATUS',
  'pymysql.converters',
  'pymysql.cursors',
  'pymysql.err',
  'pymysql.optionfile',
  'pymysql.protocol',
  'pymysql.times'],
 ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\_pyinstaller',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('PyMySQL-1.1.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\LICENSE',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\METADATA',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\RECORD',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\WHEEL',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\top_level.txt',
   'DATA'),
  ('pymysql\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\__init__.py',
   'DATA'),
  ('pymysql\\_auth.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\_auth.py',
   'DATA'),
  ('pymysql\\charset.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\charset.py',
   'DATA'),
  ('pymysql\\connections.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\connections.py',
   'DATA'),
  ('pymysql\\constants\\CLIENT.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'DATA'),
  ('pymysql\\constants\\COMMAND.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'DATA'),
  ('pymysql\\constants\\CR.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'DATA'),
  ('pymysql\\constants\\ER.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'DATA'),
  ('pymysql\\constants\\FIELD_TYPE.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'DATA'),
  ('pymysql\\constants\\FLAG.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\FLAG.py',
   'DATA'),
  ('pymysql\\constants\\SERVER_STATUS.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'DATA'),
  ('pymysql\\constants\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'DATA'),
  ('pymysql\\converters.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\converters.py',
   'DATA'),
  ('pymysql\\cursors.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\cursors.py',
   'DATA'),
  ('pymysql\\err.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\err.py',
   'DATA'),
  ('pymysql\\optionfile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\optionfile.py',
   'DATA'),
  ('pymysql\\protocol.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\protocol.py',
   'DATA'),
  ('pymysql\\times.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\times.py',
   'DATA'),
  ('requests-2.32.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\INSTALLER',
   'DATA'),
  ('requests-2.32.3.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\LICENSE',
   'DATA'),
  ('requests-2.32.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\METADATA',
   'DATA'),
  ('requests-2.32.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\RECORD',
   'DATA'),
  ('requests-2.32.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\REQUESTED',
   'DATA'),
  ('requests-2.32.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\WHEEL',
   'DATA'),
  ('requests-2.32.3.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\top_level.txt',
   'DATA'),
  ('requests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__init__.py',
   'DATA'),
  ('requests\\__version__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__version__.py',
   'DATA'),
  ('requests\\_internal_utils.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\_internal_utils.py',
   'DATA'),
  ('requests\\adapters.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\adapters.py',
   'DATA'),
  ('requests\\api.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\api.py',
   'DATA'),
  ('requests\\auth.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\auth.py',
   'DATA'),
  ('requests\\certs.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\certs.py',
   'DATA'),
  ('requests\\compat.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\compat.py',
   'DATA'),
  ('requests\\cookies.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\cookies.py',
   'DATA'),
  ('requests\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\exceptions.py',
   'DATA'),
  ('requests\\help.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\help.py',
   'DATA'),
  ('requests\\hooks.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\hooks.py',
   'DATA'),
  ('requests\\models.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\models.py',
   'DATA'),
  ('requests\\packages.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\packages.py',
   'DATA'),
  ('requests\\sessions.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\sessions.py',
   'DATA'),
  ('requests\\status_codes.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\status_codes.py',
   'DATA'),
  ('requests\\structures.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\structures.py',
   'DATA'),
  ('requests\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\utils.py',
   'DATA')],
 '3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit '
 '(AMD64)]',
 [('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('请求获取面单数据_第五版',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\无忧的面单\\请求获取面单数据_第五版.py',
   'PYSOURCE')],
 [('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copy.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hashlib.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bisect.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\struct.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\quopri.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shlex.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\platform.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\configparser.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cgi.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\netrc.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\glob.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site.py',
   'PYMODULE'),
  ('sitecustomize',
   'C:\\Program Files\\JetBrains\\PyCharm '
   '2024.1.4\\plugins\\python\\helpers\\pycharm_matplotlib_backend\\sitecustomize.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('readline',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\logger.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.release',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\release.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tty.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipimport.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pickle.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pdb.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codeop.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\opcode.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cmd.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\inspect.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ast.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\imp.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\plistlib.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.FLAG',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\FLAG.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.help',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\help.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\doctest.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('tzlocal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzlocal\\__init__.py',
   'PYMODULE'),
  ('tzlocal.unix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzlocal\\unix.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('tzlocal.win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzlocal\\win32.py',
   'PYMODULE'),
  ('tzlocal.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzlocal\\utils.py',
   'PYMODULE'),
  ('tzlocal.windows_tz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzlocal\\windows_tz.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\signal.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\smtplib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('utils', 'C:\\Users\\<USER>\\Desktop\\公司脚本\\无忧的面单\\utils.py', 'PYMODULE'),
  ('Crypto.Cipher.PKCS1_v1_5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\PKCS1_v1_5.py',
   'PYMODULE'),
  ('Crypto.Cipher._pkcs1_oaep_decode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_oaep_decode.py',
   'PYMODULE'),
  ('Crypto.Util._raw_api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_raw_api.py',
   'PYMODULE'),
  ('Crypto.Util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\__init__.py',
   'PYMODULE'),
  ('Crypto.Util._cpu_features',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_cpu_features.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('Crypto.Util._file_system',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_file_system.py',
   'PYMODULE'),
  ('Crypto.Util.py3compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\py3compat.py',
   'PYMODULE'),
  ('Crypto.Util.number',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\number.py',
   'PYMODULE'),
  ('Crypto.Random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Random\\__init__.py',
   'PYMODULE'),
  ('Crypto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\__init__.py',
   'PYMODULE'),
  ('Crypto.Hash.HMAC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\HMAC.py',
   'PYMODULE'),
  ('Crypto.Util.strxor',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\strxor.py',
   'PYMODULE'),
  ('Crypto.Hash.MD5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\MD5.py',
   'PYMODULE'),
  ('Crypto.Hash.CMAC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\CMAC.py',
   'PYMODULE'),
  ('Crypto.Hash.BLAKE2s',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\BLAKE2s.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_512',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_512.py',
   'PYMODULE'),
  ('Crypto.Hash.keccak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\keccak.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_384',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA3_224',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA3_224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA512',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA512.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA384',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA384.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA256.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA224',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA224.py',
   'PYMODULE'),
  ('Crypto.Hash.SHA1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\SHA1.py',
   'PYMODULE'),
  ('Crypto.Cipher',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\DES3.py',
   'PYMODULE'),
  ('Crypto.Cipher.ARC2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\ARC2.py',
   'PYMODULE'),
  ('Crypto.Cipher.DES',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\DES.py',
   'PYMODULE'),
  ('Crypto.Cipher.AES',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\AES.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ocb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ocb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_gcm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_gcm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_siv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_siv.py',
   'PYMODULE'),
  ('Crypto.Protocol.KDF',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Protocol\\KDF.py',
   'PYMODULE'),
  ('Crypto.Protocol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Protocol\\__init__.py',
   'PYMODULE'),
  ('Crypto.Cipher._EKSBlowfish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_EKSBlowfish.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_eax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_eax.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ccm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ccm.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_openpgp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_openpgp.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ctr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ctr.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ofb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ofb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cfb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cfb.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_cbc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_cbc.py',
   'PYMODULE'),
  ('Crypto.Cipher._mode_ecb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_mode_ecb.py',
   'PYMODULE'),
  ('Crypto.PublicKey.RSA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\RSA.py',
   'PYMODULE'),
  ('Crypto.PublicKey._openssh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_openssh.py',
   'PYMODULE'),
  ('Crypto.IO.PEM',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\IO\\PEM.py',
   'PYMODULE'),
  ('Crypto.Util.Padding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\Padding.py',
   'PYMODULE'),
  ('Crypto.IO.PKCS8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\IO\\PKCS8.py',
   'PYMODULE'),
  ('Crypto.IO._PBES',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\IO\\_PBES.py',
   'PYMODULE'),
  ('Crypto.IO',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\IO\\__init__.py',
   'PYMODULE'),
  ('Crypto.Math.Primality',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\Primality.py',
   'PYMODULE'),
  ('Crypto.Math',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\__init__.py',
   'PYMODULE'),
  ('Crypto.Math.Numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\Numbers.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerNative',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\_IntegerNative.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerBase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\_IntegerBase.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerCustom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\_IntegerCustom.py',
   'PYMODULE'),
  ('Crypto.Random.random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Random\\random.py',
   'PYMODULE'),
  ('Crypto.Math._IntegerGMP',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\_IntegerGMP.py',
   'PYMODULE'),
  ('Crypto.Util.asn1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\asn1.py',
   'PYMODULE'),
  ('Crypto.PublicKey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\__init__.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\secrets.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pathlib.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('pymysql',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE')],
 [('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_x25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_x25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-path-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-path-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-11\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-11\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-11\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-11\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-11\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-11\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-11\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('PyMySQL-1.1.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\LICENSE',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\METADATA',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\RECORD',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\WHEEL',
   'DATA'),
  ('PyMySQL-1.1.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyMySQL-1.1.1.dist-info\\top_level.txt',
   'DATA'),
  ('pymysql\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\__init__.py',
   'DATA'),
  ('pymysql\\_auth.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\_auth.py',
   'DATA'),
  ('pymysql\\charset.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\charset.py',
   'DATA'),
  ('pymysql\\connections.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\connections.py',
   'DATA'),
  ('pymysql\\constants\\CLIENT.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'DATA'),
  ('pymysql\\constants\\COMMAND.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'DATA'),
  ('pymysql\\constants\\CR.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'DATA'),
  ('pymysql\\constants\\ER.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'DATA'),
  ('pymysql\\constants\\FIELD_TYPE.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'DATA'),
  ('pymysql\\constants\\FLAG.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\FLAG.py',
   'DATA'),
  ('pymysql\\constants\\SERVER_STATUS.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'DATA'),
  ('pymysql\\constants\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'DATA'),
  ('pymysql\\converters.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\converters.py',
   'DATA'),
  ('pymysql\\cursors.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\cursors.py',
   'DATA'),
  ('pymysql\\err.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\err.py',
   'DATA'),
  ('pymysql\\optionfile.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\optionfile.py',
   'DATA'),
  ('pymysql\\protocol.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\protocol.py',
   'DATA'),
  ('pymysql\\times.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pymysql\\times.py',
   'DATA'),
  ('requests-2.32.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\INSTALLER',
   'DATA'),
  ('requests-2.32.3.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\LICENSE',
   'DATA'),
  ('requests-2.32.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\METADATA',
   'DATA'),
  ('requests-2.32.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\RECORD',
   'DATA'),
  ('requests-2.32.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\REQUESTED',
   'DATA'),
  ('requests-2.32.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\WHEEL',
   'DATA'),
  ('requests-2.32.3.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests-2.32.3.dist-info\\top_level.txt',
   'DATA'),
  ('requests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__init__.py',
   'DATA'),
  ('requests\\__version__.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\__version__.py',
   'DATA'),
  ('requests\\_internal_utils.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\_internal_utils.py',
   'DATA'),
  ('requests\\adapters.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\adapters.py',
   'DATA'),
  ('requests\\api.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\api.py',
   'DATA'),
  ('requests\\auth.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\auth.py',
   'DATA'),
  ('requests\\certs.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\certs.py',
   'DATA'),
  ('requests\\compat.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\compat.py',
   'DATA'),
  ('requests\\cookies.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\cookies.py',
   'DATA'),
  ('requests\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\exceptions.py',
   'DATA'),
  ('requests\\help.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\help.py',
   'DATA'),
  ('requests\\hooks.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\hooks.py',
   'DATA'),
  ('requests\\models.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\models.py',
   'DATA'),
  ('requests\\packages.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\packages.py',
   'DATA'),
  ('requests\\sessions.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\sessions.py',
   'DATA'),
  ('requests\\status_codes.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\status_codes.py',
   'DATA'),
  ('requests\\structures.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\structures.py',
   'DATA'),
  ('requests\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\requests\\utils.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\公司脚本\\无忧的面单\\build\\定时任务调度器_简单版\\base_library.zip',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\RECORD',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-42.0.8.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography-42.0.8.dist-info\\METADATA',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tzdata\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tzdata\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tzdata\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Factory',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tzdata\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Iceland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Iran',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tzdata\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tzdata\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\leapseconds',
   'DATA'),
  ('tzdata\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tzdata\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Cuba',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tzdata\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tzdata\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('tzdata\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Turkey',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tzdata\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\MET',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tzdata\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tzdata\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tzdata\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\CET',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tzdata\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tzdata\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\ROC',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tzdata\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tzdata\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\HST',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tzdata\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tzdata\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Poland',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tzdata\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Portugal',
   'DATA'),
  ('tzdata\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\EST',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tzdata\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\WET',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('tzdata\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tzdata\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Recife',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tzdata\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\zone.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tzdata\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\W-SU',
   'DATA'),
  ('tzdata\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Egypt',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tzdata\\zones',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zones',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Hongkong',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tzdata\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\ROK',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tzdata\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\PRC',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tzdata\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tzdata\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Libya',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\MST',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tzdata\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Israel',
   'DATA'),
  ('tzdata\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Japan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tzdata\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\GB',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\NZ',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tzdata\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tzdata\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tzdata\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Navajo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tzdata\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\EET',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('tzdata\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-65.5.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\setuptools-65.5.0.dist-info\\top_level.txt',
   'DATA')])
