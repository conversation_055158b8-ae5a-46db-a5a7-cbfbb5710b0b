# -*- coding: utf-8 -*-
"""
配置文件
将数据库配置和API配置分离
"""

# 状态选择枚举
STATUS_CHOICES = [
    ('not_uploaded', '未上传'),
    ('uploaded', '已上传'),
    ('upload_failed', '上传失败'),
]

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'sgm',
    'password': 'edfp.md4321',
    'database': 'segmart_erp',
    'charset': 'utf8mb4'
}

# API配置
API_CONFIG = {
    'base_url': 'https://oms.wydgroup.com',
    'query_url': '/oms/retailOrder/queryPageList',
    'customer_code': 'SZSXGM',
    'page_size': 2000,
    'months_back': 3
}

# 请求头模板
HEADERS_TEMPLATE = {
    'Accept': 'application/json;charset=UTF-8',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'CLUSTER-CODE': 'CN',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json;charset=UTF-8',
    'LOG-OPERATE': 'CUSTOMER_TYPE',
    'MenuId': 'M017',
    'Pragma': 'no-cache',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'clientType': 'OMS',
    'language-key': 'zh-CN',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}