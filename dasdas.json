{"log": {"access": "none", "dnsLog": false, "error": "./error.log", "loglevel": "warning"}, "api": {"tag": "api", "services": ["HandlerService", "LoggerService", "StatsService"]}, "inbounds": [{"tag": "api", "listen": "127.0.0.1", "port": 62789, "protocol": "dokodemo-door", "settings": {"address": "127.0.0.1"}}], "outbounds": [{"tag": "direct", "protocol": "freedom", "settings": {"domainStrategy": "UseIP"}}, {"tag": "blocked", "protocol": "blackhole", "settings": {}}, {"tag": "ny-amazon-11-130", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "tiktok-v5-0142", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "tiktok-v5-3551", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "tiktok-v5-4065", "protocol": "http", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "tiktok-v5-1234", "protocol": "http", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "tiktok-v5-5678", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "tiktok-v5-9876", "protocol": "http", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "tiktok-v5-3435", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "tiktok-v5-9196", "protocol": "http", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "tiktok-v5-8246", "protocol": "http", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "tiktok-v5-3411", "protocol": "http", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "tiktok-119-32", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "TK-154-57", "protocol": "http", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "TK-170-231", "protocol": "http", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "TK-70-167", "protocol": "http", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "woerma-************", "protocol": "socks", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "socks", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "woerma-************", "protocol": "socks", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "socks", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "woerma-**************", "protocol": "socks", "settings": {"servers": [{"address": "**************", "port": 2000}]}}, {"tag": "woerma-************", "protocol": "http", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "woerma-***************", "protocol": "http", "settings": {"servers": [{"address": "***************", "port": 2000}]}}, {"tag": "woerma-***************", "protocol": "http", "settings": {"servers": [{"address": "***************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "woerma-************", "protocol": "http", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "tiktok-2222222", "protocol": "http", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "segmart-54336", "protocol": "http", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "Segmart-IG-FB-TO-30865", "protocol": "http", "settings": {"servers": [{"address": "***************", "port": 2000}]}}, {"tag": "woerma-************", "protocol": "socks", "settings": {"servers": [{"address": "************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "socks", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "socks", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "woerma-*************", "protocol": "socks", "settings": {"servers": [{"address": "*************", "port": 2000}]}}, {"tag": "woerma-************", "protocol": "socks", "settings": {"servers": [{"address": "************", "port": 2000}]}}], "policy": {"levels": {"0": {"statsUserDownlink": true, "statsUserUplink": true}}, "system": {"statsInboundDownlink": true, "statsInboundUplink": true, "statsOutboundDownlink": true, "statsOutboundUplink": true}}, "routing": {"domainStrategy": "AsIs", "rules": [{"type": "field", "inboundTag": ["api"], "outboundTag": "api"}, {"inboundTag": ["inbound-59518"], "type": "field", "outboundTag": "ny-amazon-11-130"}, {"inboundTag": ["inbound-18683"], "type": "field", "outboundTag": "tiktok-v5-3435"}, {"inboundTag": ["inbound-14120"], "type": "field", "outboundTag": "tiktok-v5-1234"}, {"inboundTag": ["inbound-53506"], "type": "field", "outboundTag": "tiktok-v5-3551"}, {"inboundTag": ["inbound-59670"], "type": "field", "outboundTag": "tiktok-v5-0142"}, {"inboundTag": ["inbound-37861"], "type": "field", "outboundTag": "TK-170-231"}, {"inboundTag": ["inbound-18880"], "type": "field", "outboundTag": "tiktok-v5-4065"}, {"inboundTag": ["inbound-48983"], "type": "field", "outboundTag": "tiktok-v5-9196"}, {"inboundTag": ["inbound-35548"], "type": "field", "outboundTag": "tiktok-v5-8246"}, {"inboundTag": ["inbound-21244"], "type": "field", "outboundTag": "tiktok-v5-3411"}, {"inboundTag": ["inbound-30865"], "type": "field", "outboundTag": "tiktok-119-32"}, {"inboundTag": ["inbound-16729"], "type": "field", "outboundTag": "woerma-************"}, {"inboundTag": ["inbound-56680"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-56319"], "type": "field", "outboundTag": "woerma-************"}, {"inboundTag": ["inbound-13084"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-30740"], "type": "field", "outboundTag": "woerma-**************"}, {"inboundTag": ["inbound-21644"], "type": "field", "outboundTag": "woerma-************"}, {"inboundTag": ["inbound-19654"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-15054"], "type": "field", "outboundTag": "woerma-***************"}, {"inboundTag": ["inbound-48030"], "type": "field", "outboundTag": "woerma-***************"}, {"inboundTag": ["inbound-47125"], "type": "field", "outboundTag": "tiktok-2222222"}, {"inboundTag": ["inbound-27944"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-29192"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-11998"], "type": "field", "outboundTag": "woerma-************"}, {"inboundTag": ["inbound-54340"], "type": "field", "outboundTag": "Segmart-IG-FB-TO-30865"}, {"inboundTag": ["inbound-11998"], "type": "field", "outboundTag": "woerma-************"}, {"inboundTag": ["inbound-54336"], "type": "field", "outboundTag": "segmart-54336"}, {"inboundTag": ["inbound-26704"], "type": "field", "outboundTag": "woerma-************"}, {"inboundTag": ["inbound-22028"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-32576"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-25034"], "type": "field", "outboundTag": "woerma-*************"}, {"inboundTag": ["inbound-36404"], "type": "field", "outboundTag": "woerma-************"}, {"type": "field", "outboundTag": "blocked", "ip": ["geoip:private"]}, {"type": "field", "outboundTag": "blocked", "protocol": ["bittorrent"]}]}, "stats": {}}