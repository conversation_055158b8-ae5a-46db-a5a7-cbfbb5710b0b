#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日期计算工具
功能：获取当天和上上上个月同一天的日期（考虑跨年情况）
"""

from datetime import datetime, timedelta
import calendar


def get_dates(input_date=None, date_format="%Y-%m-%d"):
    """
    获取当天日期和上上上个月同一天的日期

    参数:
        input_date (str, optional): 输入的日期字符串，默认为None（使用当天）
        date_format (str): 日期格式，默认为 "%Y-%m-%d"

    返回:
        tuple: (当天日期字符串, 上上上个月同一天日期字符串)
    """

    # 如果没有提供输入日期，使用当天
    if input_date is None:
        current_date = datetime.now()
    else:
        # 解析输入的日期字符串
        current_date = datetime.strptime(input_date, date_format)

    # 获取当天日期
    today_str = current_date.strftime(date_format)

    # 计算上上上个月的日期
    target_year = current_date.year
    target_month = current_date.month - 3
    target_day = current_date.day

    # 处理跨年情况
    if target_month <= 0:
        target_year -= 1
        target_month += 12

    # 处理月份天数不同的情况
    # 获取目标月份的最大天数
    max_days_in_target_month = calendar.monthrange(target_year, target_month)[1]

    # 如果原日期的天数超过目标月份的最大天数，使用目标月份的最后一天
    if target_day > max_days_in_target_month:
        target_day = max_days_in_target_month

    # 创建上上上个月的日期
    three_months_ago = datetime(target_year, target_month, target_day)
    three_months_ago_str = three_months_ago.strftime(date_format)

    return today_str, three_months_ago_str



def get_time_range(input_date=None, date_format="%Y-%m-%d"):
    """
    获取时间范围：上上上个月同一天00:00:00 到 当前日期23:59:59

    参数:
        input_date (str, optional): 输入的日期字符串，默认为None（使用当天）
        date_format (str): 输入日期格式，默认为 "%Y-%m-%d"

    返回:
        tuple: (开始时间字符串, 结束时间字符串) 格式为 "%Y-%m-%d %H:%M:%S"
    """

    # 如果没有提供输入日期，使用当天
    if input_date is None:
        current_date = datetime.now()
    else:
        # 解析输入的日期字符串
        current_date = datetime.strptime(input_date, date_format)

    # 计算上上上个月的日期
    target_year = current_date.year
    target_month = current_date.month - 3
    target_day = current_date.day

    # 处理跨年情况
    if target_month <= 0:
        target_year -= 1
        target_month += 12

    # 处理月份天数不同的情况
    max_days_in_target_month = calendar.monthrange(target_year, target_month)[1]

    if target_day > max_days_in_target_month:
        target_day = max_days_in_target_month

    # 创建上上上个月同一天的00:00:00作为开始时间
    start_time = datetime(target_year, target_month, target_day, 0, 0, 0)
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")

    # 创建当前日期的23:59:59作为结束时间
    end_time = datetime(current_date.year, current_date.month, current_date.day, 23, 59, 59)
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")

    return start_time_str, end_time_str


def main():
    """
    主函数 - 演示功能
    """
    print("=" * 60)
    print("日期计算工具")
    print("=" * 60)

    # 示例1：使用当前日期
    print("\n1. 使用当前日期:")
    today, three_months_ago = get_dates()
    print(f"当天日期: {today}")
    print(f"上上上个月同一天: {three_months_ago}")

    # 示例2：使用指定日期
    print("\n2. 使用指定日期 (2025-03-25):")
    today, three_months_ago = get_dates("2025-03-25")
    print(f"指定日期: {today}")
    print(f"上上上个月同一天: {three_months_ago}")


    # 新增功能：时间范围
    print("\n" + "=" * 60)
    print("时间范围功能 (开始时间: 上上上个月00:00:00, 结束时间: 当前日期23:59:59)")
    print("=" * 60)

    # 示例5：时间范围 - 当前日期
    print("\n5. 时间范围 - 使用当前日期:")
    start_time, end_time = get_time_range()
    print(f"开始时间: {start_time}")
    print(f"结束时间: {end_time}")

    # 示例6：时间范围 - 指定日期 2025-03-25
    print("\n6. 时间范围 - 指定日期 (2025-03-25):")
    start_time, end_time = get_time_range()
    print(f"开始时间: {start_time}")
    print(f"结束时间: {end_time}")



if __name__ == "__main__":
    main() 