from pathlib import Path
import requests
import secrets


def r(e=21):
    """
    生成随机字符串，模拟JavaScript中的crypto.getRandomValues功能

    参数:
        e: 生成的字符串长度，默认为21

    返回:
        随机生成的字符串
    """
    result = ""

    # 生成e个随机字节
    random_bytes = secrets.token_bytes(e)

    for byte in random_bytes:
        # 对每个字节进行位与操作，限制在0-63范围内
        t = byte & 63

        if t < 36:
            # 0-35: 使用数字0-9和小写字母a-z
            result += str(t) if t < 10 else chr(ord('a') + t - 10)
        elif t < 62:
            # 36-61: 使用大写字母A-Z
            result += chr(ord('A') + t - 36)
        elif t > 62:
            # 63: 使用连字符
            result += "-"
        else:
            # 62: 使用下划线
            result += "_"

    return result


def upload_file(file_path, cookies=None):
    """
    上传文件到服务器

    参数:
        file_path: 文件路径（字符串或Path对象）
        cookies: 可选的cookie字典，如果不提供则不使用cookies

    返回:
        dict: 包含上传结果的字典
            - success: bool, 是否成功
            - status_code: int, HTTP状态码
            - response: dict, 服务器响应的JSON数据（如果有）
            - message: str, 结果消息
            - raw_response: str, 原始响应文本
    """

    # 转换为Path对象以便处理
    file_path = Path(file_path)

    # 检查文件是否存在
    if not file_path.exists():
        return f'❌ 错误：文件不存在 - {file_path}'

    # 检查是否为文件
    if not file_path.is_file():
        return f'❌ 错误：路径不是文件 - {file_path}'

    print(f"📁 准备上传文件: {file_path}")

    # 生成随机批次号
    uuid_data = r(21)
    print(f"🔢 生成批次号: {uuid_data}")

    headers = {
        'Accept': 'application/json;charset=UTF-8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'CLUSTER-CODE': 'CN',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'LOG-OPERATE': 'CUSTOMER_TYPE',
        'MenuId': 'M017',
        'Origin': 'https://oms.wydgroup.com',
        'Pragma': 'no-cache',
        'Referer': 'https://oms.wydgroup.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'clientType': 'OMS',
        'cookieToken': 'M7pawx3LqGbbOYUSg+mB4aJwqlyJUYg40UVlQ+OILp1c+rlUyziFp/b8+A8DAkMHgPS1e3fLsBZVZDf2ubST4FoStBr31SkffAXNnDQiPC7Wjrrwbfam7AwiPx2TPovMu6B17Ynwje76RSsqKlcXhzQXca7kkV7wz6Ek8p/m2GX6VjwO8J1poKcFjC/1PjNMHmqCvFqW7mYXdjmVPVH+aFZF/AruNSex6cznPjMrdBan6CcGKtIpt9DjCjvHCRE091P13uwGO9T7jMJgw562VN2iRO8CnxiZMsQhnepXJsGurROiSZZbCMzLbeaLreSyKtfPaMwRQznh6v3FKr9glg==',
        'customerCode': 'SZSXGM',
        'language-key': 'zh-CN',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
    }

    try:
        # 读取文件内容
        with open(file_path, 'rb') as f:
            file_content = f.read()

        print(f"📊 文件大小: {len(file_content)} 字节")

        # 准备文件数据
        files = {
            'fileList': (file_path.name, file_content, 'image/jpeg'),
            'fileType': (None, '3'),
            'uploadBatchNo': (None, uuid_data),
            'cartoonLabelType': (None, '1'),
        }

        # 发送请求
        print("🚀 正在上传...")
        response = requests.post(
            'https://oms.wydgroup.com/oms/common/batchUploadCartoonLabelFile',
            cookies=cookies,
            headers=headers,
            files=files,
            timeout=30
        )
        print(f"📡 响应状态码: {response.status_code}")
        # 解析响应
        if response.status_code == 200:
            try:
                json_response = response.json()
                # 根据新的响应格式处理
                if json_response.get('code') == 200:
                    print("✅ 文件上传成功！")
                    return uuid_data
                elif json_response.get('code') == 'oxoms1001033':
                    msg = json_response.get('msg', '系统异常')
                    print(f"❌ 系统异常{msg}")
                    return msg
            except Exception as e:
                print(e)
        return response.text
    except Exception as e:
        raise Exception(f'❌ 上传过程中出现错误: {str(e)}')


def upload_multiple_files(file_paths, cookies=None):
    """
    批量上传多个文件

    参数:
        file_paths: 文件路径列表
        cookies: 可选的cookie字典

    返回:
        list: 每个文件的上传结果列表
    """
    results = []

    print(f"📦 开始批量上传 {len(file_paths)} 个文件...")

    for i, file_path in enumerate(file_paths, 1):
        print(f"\n🔄 [{i}/{len(file_paths)}] 上传文件...")
        result = upload_file(file_path, cookies)
        results.append({
            'file_path': str(file_path),
            'result': result
        })

        # 简短延迟避免请求过快
        import time
        time.sleep(0.5)

    # 统计结果
    success_count = sum(1 for r in results if r['result']['success'])
    print(f"\n📈 批量上传完成: {success_count}/{len(file_paths)} 个文件成功")
    return results


def findLabelFileUploadInfo(uuid_data, max_retries=60, retry_interval=2):
    """
    查询文件上传状态，轮询直到上传完成

    参数:
        uuid_data: 上传批次号
        max_retries: 最大重试次数，默认60次
        retry_interval: 重试间隔秒数，默认2秒

    返回:
        dict: 包含上传状态的字典
            - success: bool, 是否成功
            - data: dict, 完整的响应数据
            - message: str, 状态消息
    """
    import time

    headers = {
        'Accept': 'application/json;charset=UTF-8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'CLUSTER-CODE': 'CN',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'LOG-OPERATE': 'CUSTOMER_TYPE',
        'MenuId': 'M017',
        'Pragma': 'no-cache',
        'Referer': 'https://oms.wydgroup.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'clientType': 'OMS',
        'cookieToken': 'fujEFCxs0sRTYqYAIv2qwx3iL2XhjtIQCIFxvgQj75dUvBoypWXZ07Jbuz4QvtLrx/VtV/CcBP4FM1/+h22sJ6nULPblgkxQxQvL9Tu6X5jIpNLLIN4H+rESfYdtl0jKTJEG9eJg8m7cASeAeS20+ENr+5NLPZB+nrUkxJYUN0DvI1YofKLxibGv6B1GwPT52o1ZS+MnCcIx7mANBSjSa2svb4uWMkxxFkisnQvWlazYBqiTdTMOaSpb5DrKty2TtOPKR7MFrM3aYqahtXsyUvkXii97tNO56mf7GDWjrvBDzbHSgl6jCe4WCKHJH+tcqjfUz6EKNOd5JyTSHnARWA==',
        'customerCode': 'SZSXGM',
        'language-key': 'zh-CN',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        # 'Cookie': 'acw_tc=1a0c384f17500559139601956e006fdfc57bf8d4aa026cac6607a439cb1995; SECKEY_ABVK=hYIrB28xrJl7b+rHiKnejKER3IvLxkk1JsQ0LcGIZRM%3D; BMAP_SECKEY=sC1ukTD2KGfDVBLyL7cvylqcm2saHLeg4kqJ98gHJE4caic7olyIV4ku5OF0nBh6KnDDZpIHvBza0EJ_OcG9OGTZxyxqCToALMS5PYVVq-p2Z3oGUkZOmwduRb0lE6QKijnjczoFuOBs7zsqb_wp73wPgS3Ui2ISi2sgCwr-K1vUuOK7epKMjwmd_oAIaMlN; isFirsetLogin=0; unitType=cm/kg; unitCode=0; language=zh; sidebarStatus=1',
    }

    params = {
        'uploadBatchNo': uuid_data,
    }

    print(f"🔍 开始查询上传状态，批次号: {uuid_data}")

    for attempt in range(max_retries):
        try:
            response = requests.get(
                'https://oms.wydgroup.com/oms/common/findLabelFileUploadInfo',
                params=params,
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                try:
                    json_data = response.json()

                    # 检查API响应是否成功
                    if json_data.get('code') == 200:
                        data_list = json_data.get('data', [])

                        if not data_list:
                            print("❌ 未找到上传文件信息")
                            return {
                                'success': False,
                                'data': json_data,
                                'message': '未找到上传文件信息'
                            }

                        # 检查第一个文件的上传状态
                        file_info = data_list[0]
                        upload_status = file_info.get('uploadStatus', '')
                        old_file_name = file_info.get('oldFileName', '')
                        error_msg = file_info.get('errorMsg', '')

                        if upload_status == '0':
                            # 上传进行中，继续轮询
                            print(f"⏳ [{attempt + 1}/{max_retries}] 文件 {old_file_name} 上传中，{retry_interval}秒后重试...")
                            time.sleep(retry_interval)
                            continue
                        elif upload_status == '1':
                            # 上传成功
                            file_path = file_info.get('path', '')
                            print(f"✅ 文件 {old_file_name} 上传成功！")
                            print(f"📁 文件路径: {file_path}")
                            return
                        elif upload_status == '2':
                            # 上传失败
                            print(f"❌ 文件 {old_file_name} 上传失败: {error_msg}")
                            return
                        else:
                            # 未知状态
                            print(f"❓ 未知上传状态: {upload_status}")
                            return
                    else:
                        print(f"❌ API返回错误: {json_data.get('msg', '未知错误')}")

                except Exception as e:
                    print(f"❌ 解析响应JSON失败: {e}")
                    print(f"原始响应: {response.text}")
            else:
                print(f"❌ HTTP请求失败，状态码: {response.status_code}")
                if attempt < max_retries - 1:
                    print(f"⏳ {retry_interval}秒后重试...")
                    time.sleep(retry_interval)
                    continue
        except Exception as e:
            print(f"❌ 请求过程中出现错误: {e}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_interval}秒后重试...")
                time.sleep(retry_interval)
                continue
    # 超过最大重试次数
    print(f"❌ 超过最大重试次数({max_retries})，查询失败")
    return f'查询超时，超过最大重试次数({max_retries})'


# 示例用法
if __name__ == "__main__":
    # # 单文件上传示例
    # file_path = "D:\\sdasdasdas\\165.jpg"
    # # 上传单个文件（不使用cookies）
    # result = upload_file(file_path)
    result = 'tJzIZJ62eJcO9t3iZONDf'
    findLabelFileUploadInfo(result)
