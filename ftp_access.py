#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
FTP服务器访问脚本
用于连接和操作FTP服务器上的文件
"""

import ftplib
import os
import sys
from datetime import datetime

class FTPClient:
    def __init__(self, host, username, password, port=21):
        """
        初始化FTP客户端
        
        Args:
            host: FTP服务器地址
            username: 用户名
            password: 密码
            port: FTP端口，默认21
        """
        self.host = host
        self.username = username
        self.password = password
        self.port = port
        self.ftp = None
    
    def connect(self):
        """连接到FTP服务器"""
        try:
            self.ftp = ftplib.FTP()
            self.ftp.connect(self.host, self.port)
            self.ftp.login(self.username, self.password)
            self.ftp.encoding = 'utf-8'  # 设置编码为UTF-8支持中文
            print(f"成功连接到FTP服务器: {self.host}")
            print(f"欢迎信息: {self.ftp.getwelcome()}")
            return True
        except Exception as e:
            print(f"连接FTP服务器失败: {e}")
            return False
    
    def disconnect(self):
        """断开FTP连接"""
        if self.ftp:
            try:
                self.ftp.quit()
                print("已断开FTP连接")
            except:
                self.ftp.close()
                print("强制关闭FTP连接")
    
    def list_directory(self, path=None):
        """列出目录内容"""
        if not self.ftp:
            print("请先连接FTP服务器")
            return None
        
        try:
            if path:
                # 切换到指定目录
                self.ftp.cwd(path)
                print(f"当前目录: {path}")
            else:
                print(f"当前目录: {self.ftp.pwd()}")
            
            # 获取目录列表
            files = []
            self.ftp.retrlines('LIST', files.append)
            
            print("\n目录内容:")
            print("-" * 60)
            for file_info in files:
                print(file_info)
            print("-" * 60)
            
            return files
        except Exception as e:
            print(f"列出目录失败: {e}")
            return None
    
    def change_directory(self, path):
        """切换目录"""
        if not self.ftp:
            print("请先连接FTP服务器")
            return False
        
        try:
            self.ftp.cwd(path)
            print(f"已切换到目录: {path}")
            print(f"当前目录: {self.ftp.pwd()}")
            return True
        except Exception as e:
            print(f"切换目录失败: {e}")
            return False
    
    def download_file(self, remote_filename, local_filename=None):
        """下载文件"""
        if not self.ftp:
            print("请先连接FTP服务器")
            return False
        
        if not local_filename:
            local_filename = remote_filename
        
        try:
            with open(local_filename, 'wb') as local_file:
                self.ftp.retrbinary(f'RETR {remote_filename}', local_file.write)
            print(f"文件下载成功: {remote_filename} -> {local_filename}")
            return True
        except Exception as e:
            print(f"下载文件失败: {e}")
            return False
    
    def upload_file(self, local_filename, remote_filename=None):
        """上传文件"""
        if not self.ftp:
            print("请先连接FTP服务器")
            return False
        
        if not remote_filename:
            remote_filename = os.path.basename(local_filename)
        
        try:
            with open(local_filename, 'rb') as local_file:
                self.ftp.storbinary(f'STOR {remote_filename}', local_file)
            print(f"文件上传成功: {local_filename} -> {remote_filename}")
            return True
        except Exception as e:
            print(f"上传文件失败: {e}")
            return False
    
    def delete_file(self, filename):
        """删除文件"""
        if not self.ftp:
            print("请先连接FTP服务器")
            return False
        
        try:
            self.ftp.delete(filename)
            print(f"文件删除成功: {filename}")
            return True
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False
    
    def create_directory(self, dirname):
        """创建目录"""
        if not self.ftp:
            print("请先连接FTP服务器")
            return False
        
        try:
            self.ftp.mkd(dirname)
            print(f"目录创建成功: {dirname}")
            return True
        except Exception as e:
            print(f"创建目录失败: {e}")
            return False

def main():
    """主函数 - 示例用法"""
    # FTP服务器配置
    # 注意：您需要提供FTP服务器的IP地址或域名
    FTP_HOST = "your-ftp-server.com"  # 请替换为实际的FTP服务器地址
    FTP_USERNAME = "xgm-wl"
    FTP_PASSWORD = "HscmiT4Cyhzp2PaM"
    TARGET_PATH = "/wwwroot/透明计划"  # FTP服务器上的路径（通常以/开头）
    
    # 创建FTP客户端
    ftp_client = FTPClient(FTP_HOST, FTP_USERNAME, FTP_PASSWORD)
    
    try:
        # 连接到FTP服务器
        if ftp_client.connect():
            # 切换到目标目录
            if ftp_client.change_directory(TARGET_PATH):
                # 列出目录内容
                ftp_client.list_directory()
                
                # 其他操作示例：
                # ftp_client.download_file("example.txt", "local_example.txt")
                # ftp_client.upload_file("local_file.txt", "remote_file.txt")
                # ftp_client.create_directory("新文件夹")
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 断开连接
        ftp_client.disconnect()

if __name__ == "__main__":
    print("=" * 60)
    print("FTP服务器访问工具")
    print("=" * 60)
    
    # 检查是否提供了FTP服务器地址
    if "your-ftp-server.com" in open(__file__).read():
        print("⚠️  警告：请先修改脚本中的FTP_HOST变量，设置正确的FTP服务器地址！")
        print("请将 'your-ftp-server.com' 替换为实际的FTP服务器IP地址或域名")
        print()
        
        # 提供一个交互式输入选项
        user_input = input("是否要手动输入FTP服务器地址？(y/n): ")
        if user_input.lower() == 'y':
            ftp_host = input("请输入FTP服务器地址: ")
            if ftp_host.strip():
                # 临时修改配置
                import types
                main_func = types.FunctionType(main.__code__.replace(
                    co_consts=tuple('your-ftp-server.com' if x == 'your-ftp-server.com' else x 
                                  for x in main.__code__.co_consts)
                ), main.__globals__)
                # 运行修改后的main函数
                exec(f"""
FTP_HOST = "{ftp_host}"
FTP_USERNAME = "xgm-wl"
FTP_PASSWORD = "HscmiT4Cyhzp2PaM"
TARGET_PATH = "/wwwroot/透明计划"

ftp_client = FTPClient(FTP_HOST, FTP_USERNAME, FTP_PASSWORD)
try:
    if ftp_client.connect():
        if ftp_client.change_directory(TARGET_PATH):
            ftp_client.list_directory()
except Exception as e:
    print(f"发生错误: {{e}}")
finally:
    ftp_client.disconnect()
""")
        else:
            print("已取消操作")
    else:
        main() 