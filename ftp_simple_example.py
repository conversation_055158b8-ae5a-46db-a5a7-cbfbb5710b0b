import ftplib
import sys

# --- FTP 服务器连接信息 ---
# 请将 '你的密码' 替换为真实的FTP密码
HOST = '*************'
PORT = 21
USERNAME = 'xgm-wl'
PASSWORD = 'HscmiT4Cyhzp2PaM'
TARGET_PATH = '/wwwroot/透明计划'

# 初始化FTP连接对象
# 使用 encoding='utf-8' 来正确处理包含中文字符的路径
try:
    ftp = ftplib.FTP(encoding='utf-8')

    # 打印调试信息，帮助排查问题
    # ftp.set_debuglevel(2) 

    print(f"正在连接到服务器: {HOST}...")
    ftp.connect(HOST, PORT)

    print(f"使用用户名 {USERNAME} 登录...")
    ftp.login(USERNAME, PASSWORD)

    # 打印服务器欢迎信息
    print("\n" + "=" * 30)
    print("服务器欢迎信息:")
    print(ftp.getwelcome())
    print("=" * 30 + "\n")

    print(f"正在尝试进入目标路径: {TARGET_PATH}...")
    # 切换到目标目录
    ftp.cwd(TARGET_PATH)

    current_path = ftp.pwd()
    print(f"成功进入目录! 当前路径: {current_path}\n")

    # 列出目录中的文件和文件夹
    print("=" * 30)
    print(f"'{current_path}' 目录下的内容:")
    print("=" * 30)

    # 使用 nlst() 获取一个简单的文件名列表
    file_list = ftp.nlst()
    if not file_list:
        print("该目录为空或无法读取列表。")
    else:
        for item in file_list:
            print(item)

except ftplib.error_perm as e:
    print(f"[错误] 权限错误: {e}", file=sys.stderr)
    print("请检查您的用户名和密码是否正确，以及该用户是否有权限访问目标路径。", file=sys.stderr)
except ftplib.error_temp as e:
    print(f"[错误] 临时错误: {e}", file=sys.stderr)
    print("服务器暂时不可用，请稍后再试。", file=sys.stderr)
except ConnectionRefusedError:
    print(f"[错误] 连接被拒绝。请检查服务器地址({HOST})和端口({PORT})是否正确，以及服务器是否正在运行。", file=sys.stderr)
except Exception as e:
    print(f"[发生未知错误]: {e}", file=sys.stderr)
finally:
    # 确保无论成功与否都关闭连接
    # hasattr 检查 ftp 对象是否已成功创建
    if 'ftp' in locals() and ftp.sock:
        print("\n正在关闭FTP连接...")
        ftp.quit()
        print("连接已关闭。")