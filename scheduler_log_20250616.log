2025-06-16 16:11:40,616 - INFO - 🚀 面单数据处理定时任务调度器启动
2025-06-16 16:11:40,620 - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-06-16 16:11:40,621 - INFO - 📋 执行首次任务...
2025-06-16 16:11:40,621 - INFO - ============================================================
2025-06-16 16:11:40,621 - INFO - 开始执行面单数据处理任务 - 2025-06-16 16:11:40.621324
2025-06-16 16:11:40,927 - ERROR - 脚本中未找到main函数
2025-06-16 16:11:40,928 - INFO - 面单数据处理任务结束 - 2025-06-16 16:11:40.928688
2025-06-16 16:11:40,928 - INFO - ============================================================
2025-06-16 16:11:40,928 - INFO - 📅 已配置的定时任务:
2025-06-16 16:11:40,928 - INFO -   - 任务ID: main_task_daily
2025-06-16 16:11:40,928 - ERROR - 调度器运行时发生错误: 'Job' object has no attribute 'next_run_time'
2025-06-16 16:11:40,930 - ERROR - 错误详情:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\公司脚本\无忧的面单\222.py", line 173, in main
    logger.info(f"  - 下次执行时间: {job.next_run_time}")
                                     ^^^^^^^^^^^^^^^^^
AttributeError: 'Job' object has no attribute 'next_run_time'

