# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/17 15:36
@Auth ： <PERSON><PERSON><PERSON><PERSON>
@File ：3333333.py
@IDE ：PyCharm
"""
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import base64
def rsa_encrypt(data,url):
    # 假设 s.c() 是一个返回字符串的函数，a 是一个列表或数组
    message = str(data) + "###" + str(url)
    # 公钥（PEM 格式）
    public_key_pem = """-----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs7d8RsgH5LtqqgUiOsLX6dl0s8pXPiGrGQRzPTyt6/30bG2zRNwHmFL7rCgM/Hm8je82cn82SCHi9PdV4AMkgfv3SOkm8PxS9VNhqwePOJzWXxhyZ/VTjyiWWE3tjMO8Ofs/VqhDiKmNT689ViFctwya6KsoL3I3WfyV6/tTbnlMwb2JN4uYbjvSjzag/I4Wv+sm3/AbCDDOMU4vCwoRuGCwcoXS7GKA3j7d2E1QKde5/xF+zdV7xdtH9pYNgifMZb9ZXo8J+WQUNH7ToSQtM7U1bevEnZTArmqagGfuyBTG4/UfFQfQD0m6ovBers05yLD9w3RlnJk4ROLnNAlGcQIDAQAB
    -----END PUBLIC KEY-----"""
    """
    使用给定的公钥对消息进行 RSA 加密，并返回 Base64 编码的加密结果。

    :param public_key_pem: 公钥（PEM 格式）
    :param message: 待加密的消息
    :return: Base64 编码的加密结果
    """
    # 加载公钥
    public_key = RSA.import_key(public_key_pem)

    # 创建加密器对象
    cipher = PKCS1_v1_5.new(public_key)

    # 使用公钥加密消息
    ciphertext = cipher.encrypt(message.encode())

    # 将加密后的消息转换为 Base64 编码
    encrypted_message_base64 = base64.b64encode(ciphertext).decode()

    return encrypted_message_base64

