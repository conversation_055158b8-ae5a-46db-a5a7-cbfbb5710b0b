# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/17 15:58
@Auth ： Tianshilin
@File ：wyd_dd.py
@IDE ：PyCharm
"""
import json

# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/26 14:23
@Auth ： Tianshilin
@File ：yam_dbg_tix.py
@IDE ：PyCharm
"""
import pymysql
import logging
from datetime import datetime
from dingtalkchatbot.chatbot import DingtalkChatbot

# 配置日志格式，包括时间信息
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

# WebHook address and secret
webhook = 'https://oapi.dingtalk.com/robot/send?access_token=547a38add615c050b722c5fbe20f91f8c092721a7cb3a1a2cc7a435eba7bd2f6'
secret = 'SEC8ae872031d007ae44cf655b120ba8fbe04655c8f9f0873a74193d3d3cc4dfa94'

# Initialize DingTalk chatbot
xiaoding = DingtalkChatbot(webhook, secret=secret)


def extract_platform_sku(path):
    """从路径中提取平台SKU"""
    try:
        # 按反斜杠分割路径
        path_parts = path.split('\\')
        # 过滤掉空字符串
        path_parts = [part for part in path_parts if part]

        # 根据路径结构，平台SKU应该在第4个位置（索引3）
        # \\\\segmart-nas\\物流相关资料-公开\\透明计划\\BST-W4220976BL-12V Ford Bronco\\28\\1.jpg
        # 索引: 0=segmart-nas, 1=物流相关资料-公开, 2=透明计划, 3=BST-W4220976BL-12V Ford Bronco
        if len(path_parts) >= 4:
            return path_parts[3]
        else:
            logging.warning("路径格式不正确，无法提取平台SKU: %s", path)
            return "未知SKU"
    except Exception as e:
        logging.error("提取平台SKU时出错: %s", e)
        return "提取失败"


def send_refund_notification(batch):
    output = []

    # 生成每个订单的信息卡片
    for i, item in enumerate(batch, 1):
        order_info = f"""
### 📋 订单 #{i}

> 📦 **参考号**: `{item['reference_number']}`  
> 🏷️ **平台SKU**: <font color='#722ED1'>**{item['platform_sku']}**</font>  
> ✅ **状态**: <font color='#52C41A'>**{item['status']}**</font>  
        """
        output.append(order_info.strip())

    # 构建完整的消息内容
    header = f"""
# 🎯 无忧达面单消息提醒

---

📊 **本次推送**: 共 **{len(batch)}** 个订单处理完成

---
    """

    footer = f"""

---

> 💡 **温馨提示**: 如有疑问请及时联系相关负责人  
> 🕐 **推送时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    """

    # 拼接完整消息
    text = header.strip() + "\n\n" + "\n\n".join(output) + footer.strip()

    logging.info("发送钉钉消息内容预览:\n%s", text)

    try:
        # Send formatted markdown message with DingTalk chatbot
        xiaoding.send_markdown(title="📢 无忧达面单处理通知", text=text)
        logging.info("钉钉消息发送成功")

        # 获取本批次的参考号列表
        reference_numbers = [item['reference_number'] for item in batch]

        # 更新钉钉发送状态
        update_dingding_sent_status(reference_numbers)

        return True
    except Exception as e:
        logging.error("钉钉消息发送失败: %s", e)
        return False


def update_dingding_sent_status(reference_numbers):
    """更新钉钉发送状态为已发送"""
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )
    try:
        with connection.cursor() as cursor:
            sql_update = """
                UPDATE sgm_tracking_record
                SET is_dingding_sent = 1
                WHERE reference_number IN (%s)
            """
            # Join reference numbers for the IN clause
            format_strings = ','.join(['%s'] * len(reference_numbers))
            logging.info("正在更新钉钉发送状态，参考号: %s", reference_numbers)
            cursor.execute(sql_update % format_strings, reference_numbers)
            logging.info("钉钉发送状态更新成功，共更新 %d 条记录", cursor.rowcount)
    except Exception as e:
        logging.error("更新钉钉发送状态时出错: %s", e)
        raise e  # 重新抛出异常，让调用方知道更新失败
    finally:
        connection.close()


def fetch_refund_orders():
    logging.info("正在从数据库中获取待处理的退款订单")
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )
    try:
        with connection.cursor() as cursor:
            sql_query = """
            SELECT reference_number,request_response,path FROM sgm_tracking_record WHERE `status` = 'upload_failed' AND is_dingding_sent = 0
            """
            cursor.execute(sql_query)
            orders = cursor.fetchall()
            logging.info("获取到 %d 条待处理订单", len(orders))
            return orders
    except Exception as e:
        logging.error("获取退款订单时出错: %s", e)
        return []
    finally:
        connection.close()


def refund_orders():
    logging.info("=======无忧达面单开始启动程序========")
    refund_orders = fetch_refund_orders()

    if not refund_orders:
        logging.info("=======没有监听数据请等待========")
        return

    batch = []
    batch_size = 5

    for item in refund_orders:
        reference_number = item[0]
        path = item[2]

        # 从路径中提取平台SKU
        platform_sku = extract_platform_sku(path)

        batch.append({
            'reference_number': reference_number,
            'platform_sku': platform_sku,
            'status': '失败'
        })

        if len(batch) == batch_size:
            logging.info("未发送无忧达面单，包含 %d 条订单", batch_size)
            send_refund_notification(batch)
            batch.clear()

    if batch:
        logging.info("未发送无忧达面单通知，包含 %d 条订单", len(batch))
        send_refund_notification(batch)
    logging.info("=======无忧达面单程序结束========")


if __name__ == '__main__':
    refund_orders()
