# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/18 9:46
@Auth ： T<PERSON><PERSON>in
@File ：wyd_status.py
@IDE ：PyCharm
"""
from datetime import datetime
import requests
import pymysql
import logging
import logging.handlers
from utils import rsa_encrypt
from dateutil.relativedelta import relativedelta
def fetch_data_from_mysql(token_name):
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,  # 启用自动提交
        charset='utf8mb4'
    )

    try:
        with connection.cursor() as cursor:
            # 执行SQL查询
            sql = "SELECT `value` FROM sgm_system_config WHERE `key` = %s"
            cursor.execute(sql, (token_name,))

            # 获取查询结果
            result = cursor.fetchone()
            if result:
                # 缓存结果，并去掉字符串的引号
                cached_cookie_value = result[0].strip('"')
                return cached_cookie_value
            else:
                return None
    finally:
        # 关闭数据库连接
        connection.close()


wyd_token = fetch_data_from_mysql('wyd_token')


def fetch_refund_orders():
    logging.info("正在从数据库中获取待处理的退款订单")
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )
    try:
        with connection.cursor() as cursor:
            sql_query = """
            SELECT reference_number FROM sgm_tracking_record WHERE `status` = 'not_uploaded'
            """
            cursor.execute(sql_query)
            orders = cursor.fetchall()
            logging.info("获取到 %d 条待处理订单", len(orders))
            return orders
    except Exception as e:
        logging.error("获取退款订单时出错: %s", e)
        return []
    finally:
        connection.close()


# 添加更新状态的函数
def update_order_status(reference_number, status_description):
    """根据reference_number更新数据库中的状态描述"""
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )
    try:
        with connection.cursor() as cursor:
            # 更新状态描述
            sql_update = """
            UPDATE sgm_tracking_record 
            SET status = %s 
            WHERE reference_number = %s
            """
            cursor.execute(sql_update, (status_description, reference_number))
            affected_rows = cursor.rowcount

            if affected_rows > 0:
                logging.info("成功更新订单 %s 的状态为: %s", reference_number, status_description)
                return True
            else:
                logging.warning("未找到订单 %s，无法更新状态", reference_number)
                return False

    except Exception as e:
        logging.error("更新订单 %s 状态时出错: %s", reference_number, e)
        return False
    finally:
        connection.close()


order_status_mapping = {
    "2": "已提交",
    "3": "拣货",
    "4": "已签出",
    "5": "已作废"
}


def refund_orders():
    cookie_token = rsa_encrypt(wyd_token,'/oms/retailOrder/queryPageList')
    logging.info("=======无忧达面单开始启动程序========")
    refund_orders = fetch_refund_orders()

    if not refund_orders:
        logging.info("=======没有监听数据请等待========")
        return

    for item in refund_orders:
        reference_number = item[0]
        headers = {
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'CLUSTER-CODE': 'CN',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json;charset=UTF-8',
            'LOG-OPERATE': 'CUSTOMER_TYPE',
            'MenuId': 'M017',
            'Origin': 'https://oms.wydgroup.com',
            'Pragma': 'no-cache',
            'Referer': 'https://oms.wydgroup.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            'clientType': 'OMS',
            'cookieToken': cookie_token,
            'customerCode': 'SZSXGM',
            'language-key': 'zh-CN',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        current_date = datetime.now()  # 获取当前时间
        past_date = current_date - relativedelta(months=3)  # 前移3个月
        endDate = current_date.date()
        startDate = past_date.date()
        json_data = {
            'orderIndex': '',
            'blurredOrderNo': '',
            'blurredSku': '',
            'product': '',
            'orderNo': '',
            'trackNo': '',
            'skuNo': '',
            'pageNo': 1,
            'pageSize': 10,
            'orderType': '',
            'expressDeliveryService': '',
            'addressType': '',
            'logisticsStatus': '',
            'dateType': '0',
            'recipientType': '',
            'remoteAreas': '',
            'submitStatus': '',
            'pickingStatus': '',
            'timeRange': [
                f'{startDate}',
                f'{endDate}',
            ],
            'creatorList': [],
            'checkStatus': '',
            'reference': f'{reference_number}',
            'recipientInfo': '',
            'outBoundSource': '',
            'cartoonLabelType': '',
            'bnNo': '',
            'snNo': '',
            'urgentChannelList': [],
            'additionalServiceList': [],
            'remoteAreaList': [],
            'warehouseCodeList': [],
            'startDate': f'{startDate}',
            'endDate': f'{endDate}',
            'orderStatus': '',
        }

        try:
            response = requests.post(
                'https://oms.wydgroup.com/oms/retailOrder/queryPageList',
                headers=headers,
                json=json_data,
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('data') and data['data'].get('records') and len(data['data']['records']) > 0:
                    orderStatus = data['data']['records'][0]['orderStatus']
                    if orderStatus == '2':
                        continue

                    status_description = order_status_mapping.get(orderStatus, "未知状态")

                    print(reference_number,status_description)
                    # 更新数据库中的状态
                    update_success = update_order_status(reference_number, status_description)
                    if update_success:
                        logging.info("订单 %s 状态更新成功", reference_number)
                    else:
                        logging.error("订单 %s 状态更新失败", reference_number)
                else:
                    logging.warning("订单 %s 在API中未找到记录", reference_number)
            else:
                logging.error("API请求失败，状态码: %d", response.status_code)
        except Exception as e:
            print("处理订单 %s 时出错: %s", reference_number, e)


if __name__ == '__main__':
    refund_orders()
