# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/13 16:04
@Auth ： <PERSON><PERSON><PERSON>in
@File ：请求获取面单数据.py
@IDE ：PyCharm
优化版本：封装为类，提高代码可维护性
"""
import requests
import pymysql
from pymysql import Error
from datetime import datetime
from utils import rsa_encrypt
from dateutil.relativedelta import relativedelta
from typing import List, Dict, Optional, Tuple
import logging
from config import DB_CONFIG, API_CONFIG, HEADERS_TEMPLATE

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理类"""

    def __init__(self, config: Dict):
        self.config = config

    def get_connection(self) -> pymysql.Connection:
        """获取数据库连接"""
        return pymysql.connect(**self.config)

    def fetch_token(self) -> Optional[str]:
        """从数据库获取token"""
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    sql = "SELECT `value` FROM sgm_system_config WHERE `key` = %s"
                    cursor.execute(sql, ('wyd_token',))
                    result = cursor.fetchone()
                    if result:
                        token = result[0].strip('"')
                        logger.info("成功获取token")
                        return token
                    else:
                        logger.warning("未找到token记录")
                        return None
        except Error as e:
            logger.error(f"获取token失败: {e}")
            return None

    def insert_tracking_records(self, records: List[Dict]) -> Tuple[bool, int]:
        """
        将tracking记录插入数据库

        Args:
            records: 包含tracking_number和reference_number的字典列表

        Returns:
            Tuple[bool, int]: (是否成功, 实际插入记录数)
        """
        if not records:
            logger.warning("没有记录需要插入")
            return False, 0

        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    # 使用INSERT IGNORE来处理唯一索引冲突
                    insert_sql = """
                    INSERT IGNORE INTO sgm_tracking_record 
                    (tracking_number, reference_number, status, create_datetime, update_datetime) 
                    VALUES (%s, %s, %s, %s, %s)
                    """

                    current_time = datetime.now()
                    insert_data = [
                        (record["tracking_number"], record["reference_number"],
                         'not_uploaded', current_time, current_time)
                        for record in records
                    ]

                    cursor.executemany(insert_sql, insert_data)
                    connection.commit()

                    inserted_count = cursor.rowcount
                    logger.info(f"成功处理 {len(records)} 条记录，实际插入 {inserted_count} 条新记录")
                    return True, inserted_count

        except Error as e:
            logger.error(f"数据库操作出错: {e}")
            return False, 0


class OrderDataFetcher:
    """订单数据获取类"""

    def __init__(self, db_manager: DatabaseManager, api_config: Dict):
        self.db_manager = db_manager
        self.api_config = api_config

    def _get_headers(self, cookie_token: str) -> Dict:
        """生成请求头"""
        headers = HEADERS_TEMPLATE.copy()
        headers.update({
            'Origin': self.api_config['base_url'],
            'Referer': f"{self.api_config['base_url']}/",
            'cookieToken': cookie_token,
            'customerCode': self.api_config['customer_code'],
        })
        return headers

    def _get_date_range(self) -> Tuple[str, str]:
        """获取日期范围"""
        current_date = datetime.now()
        past_date = current_date - relativedelta(months=self.api_config['months_back'])
        return str(past_date.date()), str(current_date.date())

    def _build_request_data(self, start_date: str, end_date: str) -> Dict:
        """构建请求数据"""
        return {
            'orderIndex': '',
            'blurredOrderNo': '',
            'blurredSku': '',
            'product': '',
            'orderNo': '',
            'trackNo': '',
            'skuNo': '',
            'pageNo': 1,
            'pageSize': self.api_config['page_size'],
            'orderType': '',
            'expressDeliveryService': '',
            'addressType': '',
            'logisticsStatus': '',
            'dateType': '0',
            'recipientType': '',
            'remoteAreas': '',
            'submitStatus': '',
            'pickingStatus': '',
            'timeRange': [start_date, end_date],
            'creatorList': [],
            'checkStatus': '',
            'reference': '',
            'recipientInfo': '',
            'outBoundSource': '',
            'cartoonLabelType': '0',
            'bnNo': '',
            'snNo': '',
            'urgentChannelList': [],
            'additionalServiceList': [],
            'remoteAreaList': [],
            'warehouseCodeList': [],
            'startDate': start_date,
            'endDate': end_date,
            'platForm': '1',
            'holdType': '',
            'orderStatus': '2',
        }

    def _extract_tracking_data(self, response_data: Dict) -> List[Dict]:
        """从API响应中提取跟踪数据"""
        if 'data' not in response_data or 'records' not in response_data['data']:
            logger.warning("API返回数据格式异常")
            return []

        records = response_data['data']['records']
        result = []

        for record in records:
            track_no = record.get("trackNo")
            one_reference = record.get("oneReference")

            if track_no and one_reference:
                result.append({
                    "tracking_number": track_no,
                    "reference_number": one_reference
                })
            else:
                logger.debug(f"跳过无效记录: trackNo={track_no}, oneReference={one_reference}")

        return result

    def fetch_order_data(self) -> Optional[List[Dict]]:
        """获取订单数据"""
        try:
            # 获取token
            token = self.db_manager.fetch_token()
            if not token:
                logger.error("无法获取token")
                return None

            # 生成cookie token
            cookie_token = rsa_encrypt(token, self.api_config['query_url'])

            # 准备请求参数
            headers = self._get_headers(cookie_token)
            start_date, end_date = self._get_date_range()
            json_data = self._build_request_data(start_date, end_date)

            logger.info(f"正在获取订单数据，日期范围: {start_date} 到 {end_date}")

            # 发送请求
            url = f"{self.api_config['base_url']}{self.api_config['query_url']}"
            response = requests.post(url, headers=headers, json=json_data, timeout=30)
            response.raise_for_status()

            data_json = response.json()

            # 提取数据
            result = self._extract_tracking_data(data_json)
            logger.info(f"从API获取到 {len(result)} 条有效记录")
            return result

        except requests.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取订单数据时发生错误: {e}")
            return None


class OrderDataProcessor:
    """订单数据处理主类"""

    def __init__(self, db_config: Dict = None, api_config: Dict = None):
        self.db_config = db_config or DB_CONFIG
        self.api_config = api_config or API_CONFIG
        self.db_manager = DatabaseManager(self.db_config)
        self.data_fetcher = OrderDataFetcher(self.db_manager, self.api_config)

    def process(self) -> bool:
        """处理订单数据的主流程"""
        try:
            logger.info("开始处理订单数据...")
            # 获取订单数据
            order_data = self.data_fetcher.fetch_order_data()
            if not order_data:
                logger.warning("未获取到任何数据")
                return False

            # 插入数据库
            success, inserted_count = self.db_manager.insert_tracking_records(order_data)
            if success:
                logger.info(f"数据处理完成，插入了 {inserted_count} 条新记录")
                return True
            else:
                logger.error("数据插入失败")
                return False
        except Exception as e:
            logger.error(f"处理订单数据时发生错误: {e}")
            return False

    def process_with_custom_date_range(self, start_date: str, end_date: str) -> bool:
        """使用自定义日期范围处理订单数据"""
        try:
            logger.info(f"开始处理订单数据，自定义日期范围: {start_date} 到 {end_date}")

            # 临时修改日期范围
            original_months_back = self.api_config['months_back']
            self.api_config['months_back'] = 0  # 禁用自动日期计算

            # 重写日期范围获取方法
            def custom_get_date_range():
                return start_date, end_date

            self.data_fetcher._get_date_range = custom_get_date_range

            # 执行处理
            result = self.process()

            # 恢复原始配置
            self.api_config['months_back'] = original_months_back

            return result

        except Exception as e:
            logger.error(f"使用自定义日期范围处理数据时发生错误: {e}")
            return False


def main():
    """主函数"""
    processor = OrderDataProcessor()
    success = processor.process()
    if success:
        logger.info("程序执行成功")
    else:
        logger.error("程序执行失败")


if __name__ == "__main__":
    main()