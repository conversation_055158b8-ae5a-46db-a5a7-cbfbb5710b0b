# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['C:/Users/<USER>/Desktop/公司脚本/无忧的面单/PDF分割.py'],
    pathex=['C:/Users/<USER>/Desktop/公司脚本/无忧的面单'],
    binaries=[],
    datas=[('C:\Users\<USER>\Desktop\公司脚本\无忧的面单\.venv\Lib\site-packages\apscheduler', 'apscheduler'), ('C:\Users\<USER>\Desktop\公司脚本\无忧的面单\.venv\Lib\site-packages\requests', 'requests'), ('C:\Users\<USER>\Desktop\公司脚本\无忧的面单\.venv\Lib\site-packages\pymysql', 'pymysql')],
    hiddenimports=[
        'apscheduler',
        'apscheduler.schedulers',
        'apscheduler.schedulers.base',
        'apscheduler.schedulers.blocking',
        'apscheduler.triggers',
        'apscheduler.triggers.base',
        'apscheduler.triggers.cron',
        'apscheduler.triggers.cron.fields',
        'apscheduler.triggers.date',
        'apscheduler.triggers.interval',
        'apscheduler.executors',
        'apscheduler.executors.base',
        'apscheduler.executors.pool',
        'apscheduler.jobstores',
        'apscheduler.jobstores.base',
        'apscheduler.jobstores.memory',
        'apscheduler.util',
        'apscheduler.events',
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.models',
        'requests.sessions',
        'requests.structures',
        'requests.utils',
        'pymysql',
        'pymysql.connections',
        'pymysql.cursors',
        'pymysql.err',
        'pymysql.constants',
        'pymysql.constants.CLIENT',
        'pymysql.constants.COMMAND',
        'pymysql.constants.ER',
        'pymysql.constants.FIELD_TYPE',
        'pymysql.constants.FLAG',
        'pymysql.constants.SERVER_STATUS',
        'logging',
        'logging.handlers',
        'pathlib',
        'secrets',
        'json',
        'datetime',
        'time',
        'os',
        'threading',
        'concurrent',
        'concurrent.futures',
        'pytz',
        'tzlocal',
        'six'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='定时任务调度器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
