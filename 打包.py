# -*- coding: utf-8 -*-
"""
打包脚本 - 将定时任务调度器.py打包成可执行文件
"""
import os
import subprocess
import sys
import shutil
import tkinter as tk
from tkinter import filedialog, messagebox


def install_dependencies():
    """安装必要的依赖包"""
    required_packages = [
        'pyinstaller',
        'apscheduler',
        'requests',
        'pymysql',
        'pathlib',
        'tzlocal',  # apscheduler 时间处理依赖
        'pytz',
        'six'
    ]

    print("检查并安装必要的依赖包...")
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"🔄 正在安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装完成")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e}")
                return False
    return True


def select_file():
    """选择要打包的Python文件"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    file_path = filedialog.askopenfilename(
        title="选择定时任务调度器.py文件",
        filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")]
    )

    root.destroy()
    return file_path


def get_package_path(package_name):
    """获取包的安装路径"""
    try:
        import importlib.util
        spec = importlib.util.find_spec(package_name)
        if spec and spec.origin:
            return os.path.dirname(spec.origin)
    except:
        pass
    return None


def create_spec_file(source_file, source_dir):
    """创建.spec文件来更精确地控制打包过程"""

    # 获取apscheduler包的路径
    apscheduler_path = get_package_path('apscheduler')
    requests_path = get_package_path('requests')
    pymysql_path = get_package_path('pymysql')

    datas_list = []
    if apscheduler_path:
        datas_list.append(f"('{apscheduler_path}', 'apscheduler')")
    if requests_path:
        datas_list.append(f"('{requests_path}', 'requests')")
    if pymysql_path:
        datas_list.append(f"('{pymysql_path}', 'pymysql')")

    datas_str = "[" + ", ".join(datas_list) + "]" if datas_list else "[]"

    spec_content = f"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{source_file}'],
    pathex=['{source_dir}'],
    binaries=[],
    datas={datas_str},
    hiddenimports=[
        'apscheduler',
        'apscheduler.schedulers',
        'apscheduler.schedulers.base',
        'apscheduler.schedulers.blocking',
        'apscheduler.triggers',
        'apscheduler.triggers.base',
        'apscheduler.triggers.cron',
        'apscheduler.triggers.cron.fields',
        'apscheduler.triggers.date',
        'apscheduler.triggers.interval',
        'apscheduler.executors',
        'apscheduler.executors.base',
        'apscheduler.executors.pool',
        'apscheduler.jobstores',
        'apscheduler.jobstores.base',
        'apscheduler.jobstores.memory',
        'apscheduler.util',
        'apscheduler.events',
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.models',
        'requests.sessions',
        'requests.structures',
        'requests.utils',
        'pymysql',
        'pymysql.connections',
        'pymysql.cursors',
        'pymysql.err',
        'pymysql.constants',
        'pymysql.constants.CLIENT',
        'pymysql.constants.COMMAND',
        'pymysql.constants.ER',
        'pymysql.constants.FIELD_TYPE',
        'pymysql.constants.FLAG',
        'pymysql.constants.SERVER_STATUS',
        'logging',
        'logging.handlers',
        'pathlib',
        'secrets',
        'json',
        'datetime',
        'time',
        'os',
        'threading',
        'concurrent',
        'concurrent.futures',
        'pytz',
        'tzlocal',
        'six'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='定时任务调度器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"""

    spec_file_path = os.path.join(source_dir, "定时任务调度器.spec")
    with open(spec_file_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print(f"已创建spec文件: {spec_file_path}")
    return spec_file_path


def try_simple_packaging(source_file, source_dir):
    """尝试使用简单的命令行参数打包"""
    print("🔄 尝试使用简单打包方式...")

    cmd = [
        "pyinstaller",
        "--noconfirm",
        "--onefile",
        "--console",
        "--collect-all", "apscheduler",
        "--collect-all", "requests",
        "--collect-all", "pymysql",
        "--hidden-import", "apscheduler",
        "--hidden-import", "apscheduler.schedulers.blocking",
        "--hidden-import", "apscheduler.schedulers",
        "--hidden-import", "apscheduler.triggers",
        "--hidden-import", "apscheduler.executors",
        "--hidden-import", "apscheduler.jobstores",
        "--hidden-import", "apscheduler.events",
        "--hidden-import", "requests",
        "--hidden-import", "pymysql",
        "--hidden-import", "pymysql.cursors",
        "--hidden-import", "tzlocal",
        "--hidden-import", "pytz",
        "--hidden-import", "six",
        "--name=定时任务调度器_简单版",
        source_file
    ]

    print("执行简单打包命令:", " ".join(cmd))

    try:
        subprocess.check_call(cmd, cwd=source_dir)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 简单打包失败: {e}")
        return False


def main():
    """主函数，执行打包操作"""
    print("=== 开始打包定时任务调度器程序 ===")

    # 安装必要的依赖
    if not install_dependencies():
        print("依赖安装失败，无法继续打包")
        input("按Enter键退出...")
        return

    # 让用户选择源文件
    print("请选择定时任务调度器.py文件...")
    source_file = select_file()

    if not source_file or not os.path.exists(source_file):
        print("未选择文件或文件不存在，退出程序")
        input("按Enter键退出...")
        return

    print(f"已选择文件: {source_file}")

    # 获取源文件所在目录
    source_dir = os.path.dirname(source_file)

    # 检查utils.py是否存在
    utils_file = os.path.join(source_dir, "utils.py")
    if not os.path.exists(utils_file):
        print("⚠️  警告: utils.py文件不存在，请确保该文件在同一目录下")
        response = input("是否继续打包? (y/n): ")
        if response.lower() != 'y':
            return

    # 切换到源文件目录
    original_dir = os.getcwd()
    os.chdir(source_dir)

    try:
        # 方法1：尝试简单打包
        if try_simple_packaging(source_file, source_dir):
            print("✅ 简单打包成功！")

            # 查找生成的可执行文件
            dist_dir = os.path.join(source_dir, "dist")
            exe_file = os.path.join(dist_dir, "定时任务调度器_简单版.exe")

            if os.path.exists(exe_file):
                print(f"✅ 可执行文件已生成: {exe_file}")

                # 创建logs目录
                logs_dir = os.path.join(dist_dir, "logs")
                os.makedirs(logs_dir, exist_ok=True)
                print(f"✅ 已创建日志目录: {logs_dir}")

                # 创建成功消息框
                root = tk.Tk()
                root.withdraw()
                messagebox.showinfo("打包成功",
                                    f"可执行文件已保存到:\n{exe_file}\n\n"
                                    f"日志将保存到:\n{logs_dir}")
                root.destroy()

                print("=== 打包完成 ===")
                return

        # 方法2：如果简单打包失败，尝试spec文件方式
        print("🔄 尝试使用spec文件打包...")
        spec_file = create_spec_file(source_file, source_dir)

        cmd = [
            "pyinstaller",
            "--noconfirm",
            "--clean",
            spec_file
        ]

        print("执行spec打包命令:", " ".join(cmd))
        subprocess.check_call(cmd)
        print("✅ spec打包成功!")

        # 查找生成的可执行文件
        dist_dir = os.path.join(source_dir, "dist")
        exe_file = os.path.join(dist_dir, "定时任务调度器.exe")

        if os.path.exists(exe_file):
            print(f"✅ 可执行文件已生成: {exe_file}")

            # 创建logs目录
            logs_dir = os.path.join(dist_dir, "logs")
            os.makedirs(logs_dir, exist_ok=True)
            print(f"✅ 已创建日志目录: {logs_dir}")

            # 创建成功消息框
            root = tk.Tk()
            root.withdraw()
            messagebox.showinfo("打包成功",
                                f"可执行文件已保存到:\n{exe_file}\n\n"
                                f"日志将保存到:\n{logs_dir}")
            root.destroy()
        else:
            print("❌ 未找到生成的可执行文件")

        print("=== 打包完成 ===")

    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print("\n🔧 建议的手动解决方案:")
        print("1. 在命令行中运行:")
        print(f"   cd \"{source_dir}\"")
        print(f"   pip install apscheduler requests pymysql")
        print(
            f"   pyinstaller --onefile --console --collect-all apscheduler --collect-all requests --collect-all pymysql \"{source_file}\"")
        print("\n2. 或者尝试虚拟环境:")
        print("   python -m venv venv")
        print("   venv\\Scripts\\activate")
        print("   pip install apscheduler requests pymysql pyinstaller")
        print(f"   pyinstaller --onefile --console --collect-all apscheduler \"{source_file}\"")

        # 创建错误消息框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("打包失败", f"打包过程中出错:\n{e}\n\n请查看控制台获取详细解决方案")
        root.destroy()

    finally:
        # 恢复原始工作目录
        os.chdir(original_dir)

    input("按Enter键退出...")


if __name__ == "__main__":
    main()