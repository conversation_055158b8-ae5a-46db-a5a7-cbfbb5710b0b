# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/13 16:04
@Auth ： T<PERSON><PERSON>in
@File ：请求获取面单数据.py
@IDE ：PyCharm
"""
import requests
import pymysql
from pymysql import Error
from datetime import datetime
from utils import rsa_encrypt
from dateutil.relativedelta import relativedelta

# 状态选择
STATUS_CHOICES = [
    ('not_uploaded', '未上传'),
    ('uploaded', '已上传'),
    ('upload_failed', '上传失败'),
]

# 数据库配置 - 请根据实际情况修改
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'sgm',
    'password': 'edfp.md4321',
    'database': 'segmart_erp',
    'charset': 'utf8mb4'
}

def fetch_data_from_mysql():
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,  # 启用自动提交
        charset='utf8mb4'
    )

    try:
        with connection.cursor() as cursor:
            # 执行SQL查询
            sql = "SELECT `value` FROM sgm_system_config WHERE `key` = %s"
            cursor.execute(sql, ('wyd_token',))

            # 获取查询结果
            result = cursor.fetchone()
            if result:
                # 缓存结果，并去掉字符串的引号
                cached_cookie_value = result[0].strip('"')
                return cached_cookie_value
            else:
                print("No result found.")
                return None
    finally:
        # 关闭数据库连接
        connection.close()


data = fetch_data_from_mysql()
url = '/oms/retailOrder/queryPageList' #需要替换调用url

cookieToken = rsa_encrypt(data,url)

headers = {
    'Accept': 'application/json;charset=UTF-8',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'CLUSTER-CODE': 'CN',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json;charset=UTF-8',
    'LOG-OPERATE': 'CUSTOMER_TYPE',
    'MenuId': 'M017',
    'Origin': 'https://oms.wydgroup.com',
    'Pragma': 'no-cache',
    'Referer': 'https://oms.wydgroup.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'clientType': 'OMS',
    'cookieToken': cookieToken,
    'customerCode': 'SZSXGM',
    'language-key': 'zh-CN',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}

current_date = datetime.now()  # 获取当前时间
past_date = current_date - relativedelta(months=3)  # 前移3个月
endDate = current_date.date()
startDate = past_date.date()

json_data = {
    'orderIndex': '',
    'blurredOrderNo': '',
    'blurredSku': '',
    'product': '',
    'orderNo': '',
    'trackNo': '',
    'skuNo': '',
    'pageNo': 1,
    'pageSize': 2000,
    'orderType': '',
    'expressDeliveryService': '',
    'addressType': '',
    'logisticsStatus': '',
    'dateType': '0',
    'recipientType': '',
    'remoteAreas': '',
    'submitStatus': '',
    'pickingStatus': '',
    'timeRange': [
        f'{startDate}',
        f'{endDate}',
    ],
    'creatorList': [],
    'checkStatus': '',
    'reference': '',
    'recipientInfo': '',
    'outBoundSource': '',
    'cartoonLabelType': '0',
    'bnNo': '',
    'snNo': '',
    'urgentChannelList': [],
    'additionalServiceList': [],
    'remoteAreaList': [],
    'warehouseCodeList': [],
    'startDate': f'{startDate}',
    'endDate': f'{endDate}',
    'platForm': '1',
    'holdType': '',
    'orderStatus': '2',
}


response = requests.post(
    'https://oms.wydgroup.com/oms/retailOrder/queryPageList',
    headers=headers,
    json=json_data,
)
data_json = response.json()
# 使用列表推导式提取trackNo和oneReference
result = [{"tracking_number": record["trackNo"], "reference_number": record["oneReference"]}
          for record in data_json["data"]["records"]]


# 数据库操作
def insert_tracking_records(records):
    """
    将tracking记录插入数据库
    reference_number是唯一索引，存在就不添加
    """
    connection = None
    try:
        # 建立数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()

        # 使用INSERT IGNORE来处理唯一索引冲突
        # 如果reference_number已存在，则忽略插入
        insert_sql = """
        INSERT IGNORE INTO sgm_tracking_record (tracking_number, reference_number, status, create_datetime, update_datetime) 
        VALUES (%s, %s, %s, %s, %s)
        """

        # 获取当前时间
        current_time = datetime.now()

        # 准备数据，添加默认status为'not_uploaded'和当前时间
        insert_data = [
            (record["tracking_number"], record["reference_number"], 'not_uploaded', current_time, current_time) for
            record in records]

        # 批量插入
        cursor.executemany(insert_sql, insert_data)

        # 提交事务
        connection.commit()

        # 获取实际插入的记录数
        inserted_count = cursor.rowcount
        print(f"成功处理 {len(records)} 条记录，实际插入 {inserted_count} 条新记录")

    except Error as e:
        print(f"数据库操作出错: {e}")
        if connection:
            connection.rollback()
    finally:
        if connection:
            cursor.close()
            connection.close()


# 执行数据库插入
if result:
    print(f"从API获取到 {len(result)} 条记录")
    insert_tracking_records(result)
    print("数据插入完成")
else:
    print("未获取到任何数据")