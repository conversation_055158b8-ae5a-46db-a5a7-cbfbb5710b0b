# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/17 14:43
@Auth ： Tians<PERSON>in
@File ：文件提醒.py
@IDE ：PyCharm
"""
# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/26 14:23
@Auth ： Tians<PERSON>in
@File ：yam_dbg_tix.py
@IDE ：PyCharm
"""
import pymysql
import logging
from dingtalkchatbot.chatbot import DingtalkChatbot

# 配置日志格式，包括时间信息
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)

# WebHook address and secret
webhook = 'https://oapi.dingtalk.com/robot/send?access_token=4fdbdeedf0bca61588787f28364fab8e14714e57070f6f71a17f3ad83dde662b'
secret = 'SECd1befc5ea5b28f46b6cca838bad57762a0ecb57722d04758d51e9d9770c137a5'

# Initialize DingTalk chatbot
xiaoding = DingtalkChatbot(webhook, secret=secret)

def send_refund_notification(batch):
    output = []
    for item in batch:
        order_info = f"""
**参考编号**: `{item['orderNumber']}`  
**跟踪号**: `{item['trackingNumber']}`  
**爬取时间**: <font color='red'> `{item['date_create']}` </font>  
        """

        output.append(order_info.strip())

    # Join all order info entries in the batch with line breaks
    text = "## 📢 亚马逊面单消息提醒\n\n" + "\n\n---\n\n".join(output)
    logging.info(text)

    # Send formatted markdown message with DingTalk chatbot
    xiaoding.send_markdown(title="退款订单通知", text=text)


def update_order_status(order_ids):
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )
    try:
        with connection.cursor() as cursor:
            sql_update = """
                UPDATE sgm_refund_order
                SET status = 1
                WHERE platform_order_no IN (%s)
            """
            # Join order IDs for the IN clause
            format_strings = ','.join(['%s'] * len(order_ids))
            logging.info("正在更新订单状态，订单ID: %s", order_ids)
            cursor.execute(sql_update % format_strings, order_ids)
            logging.info("订单状态更新成功")
    except Exception as e:
        logging.error("更新订单状态时出错: %s", e)
    finally:
        connection.close()


def fetch_refund_orders():
    logging.info("正在从数据库中获取待处理的退款订单")
    connection = pymysql.connect(
        host='*************',
        port=3306,
        user='sgm',
        password='edfp.md4321',
        database='segmart_erp',
        autocommit=True,
        charset='utf8mb4'
    )
    try:
        with connection.cursor() as cursor:
            sql_query = """
            SELECT refrence_no, shipping_method_no,date_create status FROM sgm_amazon_order WHERE date_create >= '2025-05-15' AND package_quantity >= 2 AND (status = 2 OR status = 0)
            """
            cursor.execute(sql_query)
            orders = cursor.fetchall()
            logging.info("获取到 %d 条待处理订单", len(orders))
            return orders
    except Exception as e:
            logging.error("获取退款订单时出错: %s", e)
            return []
    finally:
        connection.close()


def refund_orders():
    logging.info("=======亚马逊多包裹开始启动程序========")
    refund_orders = fetch_refund_orders()

    if not refund_orders:
        logging.info("=======没有监听数据请等待========")
        return

    batch = []
    batch_size = 5

    for item in refund_orders:
        orderNumber = item[0]
        trackingNumber = item[1]
        date_create = item[2]

        batch.append({
            'orderNumber': orderNumber,
            'trackingNumber': trackingNumber,
            'date_create': date_create,
        })

        if len(batch) == batch_size:
            logging.info("未发送亚马逊多包裹，包含 %d 条订单", batch_size)
            send_refund_notification(batch)
            batch.clear()

    if batch:
        logging.info("未发送亚马逊多包裹通知，包含 %d 条订单", len(batch))
        send_refund_notification(batch)
    logging.info("=======亚马逊多包裹程序结束========")



if __name__ == '__main__':
    refund_orders()
