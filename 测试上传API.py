# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/14 18:25
@Auth ： Tianshilin
@File ：测试上传API.py
@IDE ：PyCharm
"""
import requests
import secrets

def r(e=21):
    """
    生成随机字符串，模拟JavaScript中的crypto.getRandomValues功能

    参数:
        e: 生成的字符串长度，默认为21

    返回:
        随机生成的字符串
    """
    result = ""

    # 生成e个随机字节
    random_bytes = secrets.token_bytes(e)

    for byte in random_bytes:
        # 对每个字节进行位与操作，限制在0-63范围内
        t = byte & 63

        if t < 36:
            # 0-35: 使用数字0-9和小写字母a-z
            result += str(t) if t < 10 else chr(ord('a') + t - 10)
        elif t < 62:
            # 36-61: 使用大写字母A-Z
            result += chr(ord('A') + t - 36)
        elif t > 62:
            # 63: 使用连字符
            result += "-"
        else:
            # 62: 使用下划线
            result += "_"

    return result



headers = {
    'Accept': 'application/json;charset=UTF-8',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'CLUSTER-CODE': 'CN',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundary9tg6gTsKtl3spvIs',
    'LOG-OPERATE': 'CUSTOMER_TYPE',
    'MenuId': 'M017',
    'Origin': 'https://oms.wydgroup.com',
    'Pragma': 'no-cache',
    'Referer': 'https://oms.wydgroup.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'clientType': 'OMS',
    'cookieToken': 'hk+C9UF0VYxsCKry4Va2g1zdMIpcB92xeojCOtBMp+jnuNBHZk5/8PI/gvu+B7NmQ8NB7W7mMacvIDFue1RbOul9CJJsTfOjmjYy5MjXcZru/aMt/pSIkO6owJIw4wPNOXk79UFcvhMhlBs2sBSFvBv1bqDcP0yg5h6A9fGWqP6pOaaHjPnSAwf5a4K8+Ig/SKUZDJn8XfIJLCYAPjSHqlGv9xN7tOt2t86/b/ctilPmiwV73Z+KRHmHxbXw5AxNEkYGEvFkd7dtc/yKE/t8Azt4SujKP8UF0i1LJoNdqWPc7/zOruRUBnnCqLqnDLKvWSEGr67EsFq725lCd1WS4A==',
    'customerCode': 'SZSXGM',
    'language-key': 'zh-CN',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}

files = {
    'fileList': (r'\\segmart-nas\技术部\透明计划\VickieRD-W4220975R-24V Raptor\00850076209032\100.jpg', '', 'image/jpeg'),
    'fileType': (None, '3'),
    'uploadBatchNo': (None, 'vHXrVUi4F3nHTIlxDRwqR'),
    'cartoonLabelType': (None, '1'),
}

response = requests.post(
    'https://oms.wydgroup.com/oms/common/batchUploadCartoonLabelFile',
    headers=headers,
    files=files,
)
print(response.text)