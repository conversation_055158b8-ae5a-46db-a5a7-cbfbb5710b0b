# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/13 16:04
@Auth ： <PERSON><PERSON><PERSON><PERSON>
@File ：请求获取面单数据.py
@IDE ：PyCharm
"""
import requests
import pymysql
from pymysql import Error
from datetime import datetime
import os
from pathlib import Path

# 状态选择
STATUS_CHOICES = [
    ('not_uploaded', '未上传'),
    ('uploaded', '已上传'),
    ('upload_failed', '上传失败'),
]

# 数据库配置 - 请根据实际情况修改
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'sgm',
    'password': 'edfp.md4321',
    'database': 'segmart_erp',
    'charset': 'utf8mb4'
}

headers = {
    'Accept': 'application/json;charset=UTF-8',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'CLUSTER-CODE': 'CN',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json;charset=UTF-8',
    'LOG-OPERATE': 'CUSTOMER_TYPE',
    'MenuId': 'M017',
    'Origin': 'https://oms.wydgroup.com',
    'Pragma': 'no-cache',
    'Referer': 'https://oms.wydgroup.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'clientType': 'OMS',
    'cookieToken': 'gICaVX0Lz7R04Z7wKj/lCmbGk0jf/1ZcMGKacR8Quxv4nJSAoUng0kLbRccexHN/kMJkumhsMf9fiJJKpsLV7pc+84qchI+592CV6XQv5LBmX1/kUQDkedU3SU9tU2S0HwJDPkOcjew7vk4kjZma6RzaKnfIWsJ0h0lL3K2qRcdJ3wCJxPx1MmxQKKC/k4Kp5XRAJRwNKqvg8ihqQiSPc2CiYkqXnX1vZOXobFGmtZ0iW+jyJTZjkxgRLdUii1o9kXNTvucHRhy8CIy/CxJku4pH6qsUDg2bJeCUl5Op3Ivi3Z9VzYozeRvAPrBBfIUHLc+KAKT7r6/pot4ZqC8KsA==',
    'customerCode': 'SZSXGM',
    'language-key': 'zh-CN',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
}

json_data = {
    'orderIndex': '',
    'blurredOrderNo': '',
    'blurredSku': '',
    'product': '',
    'orderNo': '',
    'trackNo': '',
    'skuNo': '',
    'pageNo': 1,
    'pageSize': 2000,
    'orderType': '',
    'expressDeliveryService': '',
    'addressType': '',
    'logisticsStatus': '',
    'dateType': '0',
    'recipientType': '',
    'remoteAreas': '',
    'submitStatus': '',
    'pickingStatus': '',
    'timeRange': [
        '2025-03-13',
        '2025-06-13',
    ],
    'creatorList': [],
    'checkStatus': '',
    'reference': '',
    'recipientInfo': '',
    'outBoundSource': '',
    'cartoonLabelType': '0',
    'bnNo': '',
    'snNo': '',
    'urgentChannelList': [],
    'additionalServiceList': [],
    'remoteAreaList': [],
    'warehouseCodeList': [],
    'startDate': '2025-03-13',
    'endDate': '2025-06-13',
    'platForm': '1',
    'holdType': '',
    'orderStatus': '2',
}


def query_not_uploaded_orders():
    """
    查询状态为not_uploaded的所有订单
    """
    connection = None
    try:
        # 建立数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)  # 使用字典游标

        # 查询SQL
        query_sql = """
        SELECT id, tracking_number, reference_number, status
        FROM sgm_tracking_record 
        WHERE status = 'not_uploaded'
        ORDER BY create_datetime DESC
        """

        # 执行查询
        cursor.execute(query_sql)
        results = cursor.fetchall()

        return results

    except Error as e:
        print(f"查询数据库出错: {e}")
        return []
    finally:
        if connection:
            cursor.close()
            connection.close()


def query_order_sku_by_reference(reference_number):
    """
    根据单个reference_number查询订单的SKU信息
    """
    connection = None
    try:
        # 建立数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询SQL
        query_sql = """
        SELECT platform_sku, product_sku_org 
        FROM sgm_order_status_all 
        WHERE order_code = %s
        """

        # 执行查询
        cursor.execute(query_sql, (reference_number,))
        sku_results = cursor.fetchall()

        if sku_results:
            for sku_record in sku_results:
                # 添加订单号信息到结果中
                sku_record['order_code'] = reference_number
            return sku_results
        else:
            print(f"\n订单号: {reference_number} - 未找到SKU信息")
            return []

    except Error as e:
        print(f"查询订单 {reference_number} SKU信息出错: {e}")
        return []
    finally:
        if connection:
            cursor.close()
            connection.close()


def update_order_status(reference_number, new_status):
    """
    根据reference_number更新订单状态

    Args:
        reference_number: 订单参考号
        new_status: 新状态 ('uploaded', 'upload_failed')
    """
    connection = None
    try:
        # 建立数据库连接
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()

        # 更新SQL
        update_sql = """
        UPDATE sgm_tracking_record 
        SET status = %s, update_datetime = %s
        WHERE reference_number = %s
        """

        # 执行更新
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute(update_sql, (new_status, current_time, reference_number))
        connection.commit()

        print(f"订单 {reference_number} - 状态已更新为: {new_status}")
        return True

    except Error as e:
        print(f"更新订单 {reference_number} 状态出错: {e}")
        if connection:
            connection.rollback()
        return False
    finally:
        if connection:
            cursor.close()
            connection.close()


# 查询未上传的订单
print("\n" + "=" * 50)
print("查询未上传的订单:")
not_uploaded_orders = query_not_uploaded_orders()

# 如果有未上传的订单，查询它们的SKU信息
if not_uploaded_orders:
    print("\n" + "=" * 50)
    print("查询订单SKU信息:")
    print(f"=== 开始查询 {len(not_uploaded_orders)} 个订单的SKU信息 ===")

    all_sku_results = []
    for order in not_uploaded_orders:
        # 参考号
        reference_number = order['reference_number']
        # 跟踪号
        tracking_number = order['tracking_number']

        sku_results = query_order_sku_by_reference(reference_number)
        # 如果sku_results为空列表，跳过处理
        if sku_results == []:
            # 添加钉钉提醒

            continue

        product_sku_org = sku_results[0]['product_sku_org']
        # 拼接文件路径
        data_path = f'\\\\segmart-nas\\技术部\\透明计划\\{product_sku_org}'

        # 检查文件夹是否存在
        if not (os.path.exists(data_path) and os.path.isdir(data_path)):
            print(f"订单 {reference_number} - 文件夹不存在: {data_path}")
            # 这里可以添加后续处理逻辑
        # 文件夹存在，获取路径下所有文件夹的所有文件
        print(f"订单 {reference_number} - 开始获取文件夹下的所有文件: {data_path}")

        all_files = []
        try:
            # 使用os.walk递归遍历所有子目录
            for root, dirs, files in os.walk(data_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    all_files.append(file_path)


            def extract_sequence_number(file_path):
                """提取文件名中的序号用于排序"""
                try:
                    filename = os.path.basename(file_path)
                    # 去除文件扩展名
                    name_without_ext = os.path.splitext(filename)[0]
                    # 按下划线分割，获取倒数第二个部分（序号部分）
                    parts = name_without_ext.split('_')
                    if len(parts) >= 2:
                        sequence_part = parts[-2]  # 倒数第二个部分
                        # 尝试转换为数字
                        return int(sequence_part)
                except (ValueError, IndexError):
                    pass
                return float('inf')  # 如果无法提取序号，放在最后


            # 对文件列表进行排序
            all_files_sorted = sorted(all_files, key=extract_sequence_number)

            # 判断tracking_number对应的.jpg文件是否已存在
            # tracking_jpg_exists = False
            # for file_path in all_files_sorted:
            #     filename = os.path.basename(file_path)
            #     # 检查文件名是否以tracking_number开头且以.jpg结尾
            #     if filename.startswith(tracking_number) and filename.lower().endswith('.jpg'):
            #         tracking_jpg_exists = True
            #         print(f"订单 {reference_number} - 发现已存在的跟踪号文件: {filename}")
            #         break
            #
            # if tracking_jpg_exists:
            #     print(f"订单 {reference_number} - 备注: 跟踪号 {tracking_number} 对应的.jpg文件已存在，请检查是否需要更新")

            # TODO 判断文件是否存在
            # 筛选出所有包含 "未分配" 且以 .jpg 结尾的文件
            unassigned_jpg_files = [
                file for file in all_files_sorted
                if "未分配" in file and file.lower().endswith(".jpg")
            ]

            if not unassigned_jpg_files:
                print(f"订单 {reference_number} - 没有找到未分配的 .jpg 文件")
                # 更新数据库中的订单状态为上传失败
                continue

            try:
                original_file = Path(unassigned_jpg_files[0])  # 转为 Path 对象
                new_filepath = original_file.with_stem(f"{tracking_number}")  # 修改文件名
                # 重命名文件
                original_file.rename(new_filepath)
                print(f"订单 {reference_number} - 文件已重命名: {new_filepath}")

                # 更新数据库中的订单状态为已上传
                update_order_status(reference_number, 'uploaded')
            except Exception as rename_error:
                print(f"订单 {reference_number} - 文件重命名失败: {rename_error}")
                # 更新数据库中的订单状态为上传失败
                update_order_status(reference_number, 'upload_failed')

        except Exception as e:
            print(f"订单 {reference_number} - 遍历文件夹出错: {e}")
            # 更新数据库中的订单状态为上传失败
            update_order_status(reference_number, 'upload_failed')
else:
    print("没有未上传的订单需要查询SKU信息")


