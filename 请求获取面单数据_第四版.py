# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/13 16:04
@Auth ： <PERSON><PERSON><PERSON><PERSON>
@File ：请求获取面单数据.py
@IDE ：PyCharm
"""
import json
import time
import requests
import pymysql
from pymysql import Error
from datetime import datetime
import os
from pathlib import Path
import secrets
from utils import rsa_encrypt
import logging
import logging.handlers
import signal
import threading


# 配置日志系统
def setup_logging():
    """配置日志系统"""
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger("定时任务调度器")
    logger.setLevel(logging.DEBUG)

    # 清除已有的处理器
    logger.handlers.clear()

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件处理器 - 按时间轮转（每天一个文件，保留7天）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_dir / "scheduler.log",
        when='midnight',
        interval=1,
        backupCount=7,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 错误文件处理器 - 单独记录错误日志
    error_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_dir / "error.log",
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)

    # 添加处理器到记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.addHandler(error_handler)

    return logger


# 初始化日志系统
logger = setup_logging()

# 全局标志，用于控制定时任务的停止
stop_flag = threading.Event()


def signal_handler(signum, frame):
    """信号处理器，用于优雅地停止程序"""
    logger.info(f"收到信号 {signum}，正在停止定时任务...")
    stop_flag.set()


# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # 终止信号


def main_data():
    logger.info("开始执行定时任务 - 面单数据处理")

    # 数据库配置 - 请根据实际情况修改
    DB_CONFIG = {
        'host': '*************',
        'port': 3306,
        'user': 'sgm',
        'password': 'edfp.md4321',
        'database': 'segmart_erp',
        'charset': 'utf8mb4'
    }

    def r(e=21):
        """
        参数:
            e: 生成的字符串长度，默认为21
        返回:
            随机生成的字符串
        """
        result = ""

        # 生成e个随机字节
        random_bytes = secrets.token_bytes(e)

        for byte in random_bytes:
            # 对每个字节进行位与操作，限制在0-63范围内
            t = byte & 63

            if t < 36:
                # 0-35: 使用数字0-9和小写字母a-z
                result += str(t) if t < 10 else chr(ord('a') + t - 10)
            elif t < 62:
                # 36-61: 使用大写字母A-Z
                result += chr(ord('A') + t - 36)
            elif t > 62:
                # 63: 使用连字符
                result += "-"
            else:
                # 62: 使用下划线
                result += "_"

        return result

    def upload_file(file_path, fileToken):
        """
        上传文件到服务器

        参数:
            file_path: 文件路径（字符串或Path对象）
            cookies: 可选的cookie字典，如果不提供则不使用cookies

        返回:
            dict: 包含上传结果的字典
                - success: bool, 是否成功
                - status_code: int, HTTP状态码
                - response: dict, 服务器响应的JSON数据（如果有）
                - message: str, 结果消息
                - raw_response: str, 原始响应文本
        """

        # 转换为Path对象以便处理
        file_path = Path(file_path)

        # 检查文件是否存在
        if not file_path.exists():
            logger.error(f'文件不存在 - {file_path}')
            msg = {
                "msg": f'❌ 错误：文件不存在 - {file_path}'
            }
            update_order_status(reference_number, 'upload_failed', file_path, file_path.name, datetime.now(),
                                json.dumps(msg))
            return None

        # 检查是否为文件
        if not file_path.is_file():
            logger.error(f'路径不是文件 - {file_path}')
            msg = {
                "msg": f'❌ 错误：路径不是文件 - {file_path}'
            }
            update_order_status(reference_number, 'upload_failed', file_path, file_path.name, datetime.now(),
                                json.dumps(msg))
            return None

        logger.info(f"准备上传文件: {file_path}")

        # 生成随机批次号
        uuid_data = r(21)
        logger.debug(f"生成批次号: {uuid_data}")

        headers = {
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'CLUSTER-CODE': 'CN',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'LOG-OPERATE': 'CUSTOMER_TYPE',
            'MenuId': 'M017',
            'Origin': 'https://oms.wydgroup.com',
            'Pragma': 'no-cache',
            'Referer': 'https://oms.wydgroup.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'clientType': 'OMS',
            'cookieToken': fileToken,
            'customerCode': 'SZSXGM',
            'language-key': 'zh-CN',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()

            logger.debug(f"文件大小: {len(file_content)} 字节")

            # 准备文件数据
            files = {
                'fileList': (file_path.name, file_content, 'image/jpeg'),
                'fileType': (None, '3'),
                'uploadBatchNo': (None, uuid_data),
                'cartoonLabelType': (None, '1'),
            }

            # 发送请求
            logger.info("正在上传文件...")
            response = requests.post(
                'https://oms.wydgroup.com/oms/common/batchUploadCartoonLabelFile',
                headers=headers,
                files=files,
                timeout=30
            )
            logger.debug(f"响应状态码: {response.status_code}")
            # 解析响应
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    # 根据新的响应格式处理
                    if json_response.get('code') == 200:
                        logger.info("文件上传成功！")
                        return uuid_data
                    elif json_response.get('code') == 'oxoms1001033':
                        msg_data = json_response.get('msg', '系统异常')
                        logger.error(f"系统异常: {msg_data}")
                        msg = {
                            "msg": f"❌ 系统异常{msg_data}",
                            "res": json.dumps(json_response)
                        }
                        update_order_status(reference_number, 'upload_failed', file_path, file_path.name,
                                            datetime.now(),
                                            json.dumps(msg))
                        return None
                except Exception as e:
                    logger.error(f"JSON解析失败: {e}")
                    msg = {
                        "msg": f"❌ JSON解析失败:{e}",
                    }
                    update_order_status(reference_number, 'upload_failed', file_path, file_path.name, datetime.now(),
                                        json.dumps(msg))
                    return None
            return None
        except Exception as e:
            raise Exception(f'❌ 上传过程中出现错误: {str(e)}')

    def upload_multiple_files(file_paths, cookies=None):
        """
        批量上传多个文件

        参数:
            file_paths: 文件路径列表
            cookies: 可选的cookie字典

        返回:
            list: 每个文件的上传结果列表
        """
        results = []

        logger.info(f"开始批量上传 {len(file_paths)} 个文件...")

        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"[{i}/{len(file_paths)}] 上传文件: {file_path}")
            result = upload_file(file_path, cookies)
            results.append({
                'file_path': str(file_path),
                'result': result
            })

            # 简短延迟避免请求过快
            import time
            time.sleep(0.5)

        # 统计结果
        success_count = sum(1 for r in results if r['result']['success'])
        logger.info(f"批量上传完成: {success_count}/{len(file_paths)} 个文件成功")
        return results

    def findLabelFileUploadInfo(uuid_data, original_file, infoToken, max_retries=60, retry_interval=2):
        """
        查询文件上传状态，轮询直到上传完成

        参数:
            uuid_data: 上传批次号
            max_retries: 最大重试次数，默认60次
            retry_interval: 重试间隔秒数，默认2秒

        返回:
            dict: 包含上传状态的字典
                - success: bool, 是否成功
                - data: dict, 完整的响应数据
                - message: str, 状态消息
        """
        headers = {
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'CLUSTER-CODE': 'CN',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'LOG-OPERATE': 'CUSTOMER_TYPE',
            'MenuId': 'M017',
            'Pragma': 'no-cache',
            'Referer': 'https://oms.wydgroup.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'clientType': 'OMS',
            'cookieToken': infoToken,
            'customerCode': 'SZSXGM',
            'language-key': 'zh-CN',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            # 'Cookie': 'acw_tc=1a0c380a17500435473051037e006b7c0f357e39befe4920ba757e1bee6087; SECKEY_ABVK=hYIrB28xrJl7b+rHiKnejMnMJOsU//kljIgNr14roq8%3D; BMAP_SECKEY=sC1ukTD2KGfDVBLyL7cvyi_GKaiTTY8KJ-qewyfNjzRSr9_1UxJWGITQ-jvDCmyGXH1pCFjEZzknav8TlGx8hz5ubs4JoqbLwIZnilJPff40kor7gitpQXagW5ja-uNZ1OEtL6uuMYlSqDjeQnds0ewkR7SIj1QYwzutZ9vTL1uRifb4DVGJPma9RqRQg9vQ; isFirsetLogin=0; unitType=cm/kg; unitCode=0; language=zh; sidebarStatus=1',
        }
        file_path_data = Path(original_file)
        params = {
            'uploadBatchNo': uuid_data,
        }

        logger.info(f"开始查询上传状态，批次号: {uuid_data}")

        for attempt in range(max_retries):
            try:
                response = requests.get(
                    'https://oms.wydgroup.com/oms/common/findLabelFileUploadInfo',
                    params=params,
                    headers=headers,
                    timeout=10
                )

                if response.status_code == 200:
                    try:
                        json_data = response.json()

                        # 检查API响应是否成功
                        if json_data.get('code') == 200:
                            data_list = json_data.get('data', [])

                            if not data_list:
                                update_order_status(reference_number, 'upload_failed', file_path_data,
                                                    file_path_data.name)
                                return

                            # 检查第一个文件的上传状态
                            file_info = data_list[0]
                            upload_status = file_info.get('uploadStatus', '')
                            old_file_name = file_info.get('oldFileName', '')
                            error_msg = file_info.get('errorMsg', '')

                            if upload_status == '0':
                                # 上传进行中，继续轮询
                                logger.debug(
                                    f"[{attempt + 1}/{max_retries}] 文件 {old_file_name} 上传中，{retry_interval}秒后重试...")
                                time.sleep(retry_interval)
                                continue
                            elif upload_status == '1':
                                # 上传成功
                                file_path = file_info.get('path', '')
                                logger.info(f"文件 {old_file_name} 上传成功！文件路径: {file_path}")
                                msg = {
                                    "msg": f"✅ 文件 {old_file_name} 上传成功！",
                                    "res": json.dumps(json_data)
                                }
                                update_order_status(reference_number, 'uploaded', file_path_data, file_path_data.name,
                                                    datetime.now(),
                                                    json.dumps(msg))
                                return
                            elif upload_status == '2':
                                # 上传失败
                                logger.error(f"文件 {old_file_name} 上传失败: {error_msg}")
                                msg = {
                                    "msg": f"❌ 文件 {old_file_name} 上传失败: {error_msg}",
                                    "res": json.dumps(json_data)
                                }
                                update_order_status(reference_number, 'upload_failed', file_path_data,
                                                    file_path_data.name,
                                                    datetime.now(),
                                                    json.dumps(msg))
                                return
                            else:
                                # 未知状态
                                logger.warning(f"未知上传状态: {upload_status}")
                                msg = {
                                    "msg": f"❓ 未知上传状态: {upload_status}",
                                    "res": json.dumps(json_data)
                                }
                                update_order_status(reference_number, 'upload_failed', file_path_data,
                                                    file_path_data.name,
                                                    datetime.now(),
                                                    json.dumps(msg))
                                return
                        else:
                            msg = {
                                "msg": f"❌ API返回错误: {json_data.get('msg', '未知错误')}",
                                "res": json.dumps(json_data)
                            }
                            update_order_status(reference_number, 'upload_failed', file_path_data, file_path_data.name,
                                                datetime.now(),
                                                json.dumps(msg))

                    except Exception as e:
                        logger.error(f"解析响应JSON失败: {e}")
                        msg = {
                            "msg": f"❌ 解析响应JSON失败: {e}"
                        }
                        update_order_status(reference_number, 'upload_failed', file_path_data, file_path_data.name,
                                            datetime.now(),
                                            json.dumps(msg))
                else:
                    logger.warning(f"HTTP请求失败，状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        logger.debug(f"{retry_interval}秒后重试...")
                        time.sleep(retry_interval)
                        continue
            except Exception as e:
                logger.error(f"请求过程中出现错误: {e}")
                if attempt < max_retries - 1:
                    logger.debug(f"{retry_interval}秒后重试...")
                    time.sleep(retry_interval)
                    continue
        # 超过最大重试次数
        logger.error(f"超过最大重试次数({max_retries})，查询失败")
        return f'查询超时，超过最大重试次数({max_retries})'

    def query_not_uploaded_orders():
        """
        查询状态为not_uploaded的所有订单
        """
        connection = None
        try:
            # 建立数据库连接
            connection = pymysql.connect(**DB_CONFIG)
            cursor = connection.cursor(pymysql.cursors.DictCursor)  # 使用字典游标

            # 查询SQL
            query_sql = """
            SELECT id, tracking_number, reference_number, status
            FROM sgm_tracking_record 
            WHERE status = 'not_uploaded'
            """

            # 执行查询
            cursor.execute(query_sql)
            results = cursor.fetchall()

            return results

        except Error as e:
            logger.error(f"查询数据库出错: {e}")
            return []
        finally:
            if connection:
                cursor.close()
                connection.close()

    def fetch_data_from_mysql(token_name):
        connection = pymysql.connect(
            host='*************',
            port=3306,
            user='sgm',
            password='edfp.md4321',
            database='segmart_erp',
            autocommit=True,  # 启用自动提交
            charset='utf8mb4'
        )

        try:
            with connection.cursor() as cursor:
                # 执行SQL查询
                sql = "SELECT `value` FROM sgm_system_config WHERE `key` = %s"
                cursor.execute(sql, (token_name,))

                # 获取查询结果
                result = cursor.fetchone()
                if result:
                    # 缓存结果，并去掉字符串的引号
                    cached_cookie_value = result[0].strip('"')
                    return cached_cookie_value
                else:
                    logger.warning("数据库中未找到wyd_token配置")
                    return None
        finally:
            # 关闭数据库连接
            connection.close()

    wyd_token = fetch_data_from_mysql('wyd_token')
    ec_cookie = fetch_data_from_mysql('ec_cookie').replace("PHPSESSID=", "")
    url = '/oms/common/batchUploadCartoonLabelFile'  # 需要替换调用url
    url_Info = '/oms/common/findLabelFileUploadInfo'  # 需要替换调用url
    fileToken = rsa_encrypt(wyd_token, url)
    infoToken = rsa_encrypt(wyd_token, url_Info)

    def query_order_sku_by_reference_api(reference_number, ec_cookie):
        try:
            cookies = {
                'PHPSESSID': ec_cookie,
            }
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Origin': 'https://ntm1u5t-eb.eccang.com',
                'Pragma': 'no-cache',
                'Referer': 'https://ntm1u5t-eb.eccang.com/order/order-list/list/platform/amazon?display_old_page_expire_tip=0&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************-U1NPX1NZU19VU0VSIn0.Me6NlNvK6sfUDwAP9HWMhc5Ny20A-vP4j8UTjc6T5RQ&resource=sso&subjectCodeEncrypt=ntm1u5t&LANGUAGE=zh_CN&classification=&clusterPrefix=hn',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            data = {
                'isProblem': '',
                'specific_order_num': '',
                'eccang_order_suffix': '',
                'is_one_piece': '',
                'has_buyer_note': '',
                'can_merge': '',
                'type': 'ref_no',
                'code': reference_number,
                'search_rule': '',
                'buyer_type': 'buyer_name',
                'buyer_id': '',
                'site': '',
                'process_again': '',
                'ot_id': '',
                'abnormal_type': '',
                'doubt_type': '',
                'order_hold_status': '',
                'state_or_province': '',
                'city_name': '',
                'tel_or_phone': '',
                'wmsOrderStatus': '',
                'post_code_select': '1',
                'post_code_search': '',
                'date_latest_ship': '',
                'ship_service_level': '',
                'shipment_service_level_category': '',
                'audit_type': '',
                'order_oas_id': '',
                'specific_address': '',
                'createDateFrom': '',
                'createDateEnd': '',
                'payDateFrom': '',
                'payDateEnd': '',
                'verifyDateFrom': '',
                'verifyDateEnd': '',
                'shipDateFrom': '',
                'shipDateEnd': '',
                'latestShipDateFrom': '',
                'latestShipDateEnd': '',
                'markDateFrom': '',
                'markDateEnd': '',
                'order_by': '',
                'order_by_type': 'desc',
                'order_weight_from': '',
                'order_weight_end': '',
                'profit_value_from': '',
                'profit_value_end': '',
                'profit_rate_from': '',
                'profit_rate_end': '',
                'abnormal_reason': '',
                'remark': '',
                'custom_order_type': '',
                'createDateBeijingFrom': '',
                'createDateBeijingEnd': '',
                'order_note_content_amazon': '',
                'dateLatestFrom': '',
                'dateLatestEnd': '',
                'order_sku_category': '',
                'priceFrom': '',
                'priceEnd': '',
                'outStockDayFrom': '',
                'outStockDayEnd': '',
                'responsible_type': 'seller_responsible',
                'is_b2b': '',
                'is_amazon_prime': '',
                'order_type': '',
                'create_type': '',
                'has_warehouse': '',
                'customer_id': '',
                'is_split_order': '',
                'phone_exist': '',
                'sync_status': '',
                'invoice_status': '',
                'is_doubt': '',
                'profit_status': '',
                'order_splitting_method': '',
                'is_replacement_order': '',
                'is_buyer_requested_cancel': '',
                'is_buyer_customized_info_url': '',
                'is_evaluate_order': '',
                'is_face_oss_url': '',
                'has_product_problem': '',
                'has_discount': '',
                'is_additional': '',
                'has_inventory_lock': '',
                'has_operator_note': '',
                'has_email': '',
                'buyer_leave_comment': '',
                'has_refund': '',
                'platform_refund': '',
                'active_letter_status': '',
                'repeat_purchase': '',
                'return_receipt_complete': '',
                'is_fba': '',
                'is_fba_delivery': '',
                'is_cg_delivery': '',
                'is_wfs_delivery': '',
                'fba_sync_warehouse': '',
                'is_sync_status': '',
                'has_tracking_number': '',
                'shipping_method_no_change': '',
                'is_ship_fee': '',
                'logistics_status_new': '',
                'audit_type_distribution': '',
                'keyword': '',
                'platform': 'amazon',
                'status': '',
                'data_index_order_search': '',
                'is_more': '0',
            }

            response = requests.post(
                'https://ntm1u5t-eb.eccang.com/order/order-list/list/a/a/page/1/pageSize/50',
                cookies=cookies,
                headers=headers,
                data=data,
                timeout=30  # 添加超时设置
            )

            # 检查响应状态码
            if response.status_code != 200:
                logger.error(f"第一次请求失败，状态码: {response.status_code}, 订单号: {reference_number}")
                return None

            data_info = response.json()

            # 检查返回数据是否为空或无效
            if not data_info or 'data' not in data_info or not data_info['data']:
                logger.error(f"第一次请求返回数据为空或无效，订单号: {reference_number}")
                return None

            first_order_key = list(data_info['data'].keys())[0]  # 获取第一个订单的键（这里是'2353565'）

            data = {
                'order_id_arr[]': first_order_key,
                'order_status': '',
                'isProblem': '',
                'eccang_order_suffix': '',
            }

            response_info = requests.post(
                'https://ntm1u5t-eb.eccang.com/order/order-list/get-list-detail-list',
                cookies=cookies,
                headers=headers,
                data=data,
                timeout=30  # 添加超时设置
            )

            # 检查第二次响应状态码
            if response_info.status_code != 200:
                logger.error(f"第二次请求失败，状态码: {response_info.status_code}, 订单号: {reference_number}")
                return None

            response_data = response_info.json()

            # 检查返回数据结构
            if (not response_data or
                    not isinstance(response_data, list) or
                    len(response_data) == 0 or
                    'data' not in response_data[0] or
                    'order_product' not in response_data[0]['data'] or
                    not response_data[0]['data']['order_product'] or
                    'product_sku' not in response_data[0]['data']['order_product'][0]):
                logger.error(f"第二次请求返回数据结构异常，订单号: {reference_number}")
                return None

            main_product_sku = response_data[0]["data"]["order_product"][0]["product_sku"]
            logger.info(f"成功获取订单 {reference_number} 的 SKU: {main_product_sku}")
            return main_product_sku

        except requests.exceptions.Timeout:
            logger.error(f"请求超时，订单号: {reference_number}")
            return None
        except requests.exceptions.ConnectionError:
            logger.error(f"网络连接错误，订单号: {reference_number}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求异常，订单号: {reference_number}, 错误: {str(e)}")
            return None
        except json.JSONDecodeError:
            logger.error(f"JSON解析失败，订单号: {reference_number}")
            return None
        except (KeyError, IndexError, TypeError) as e:
            logger.error(f"数据结构访问异常，订单号: {reference_number}, 错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"获取订单SKU时发生未知异常，订单号: {reference_number}, 错误: {str(e)}")
            return None

    def query_order_sku_by_reference(reference_number):
        """
        根据单个reference_number查询订单的SKU信息
        """
        connection = None
        try:
            # 建立数据库连接
            connection = pymysql.connect(**DB_CONFIG)
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            # 查询SQL
            query_sql = """
            SELECT platform_sku, product_sku_org 
            FROM sgm_order_status_all 
            WHERE order_code = %s
            """

            # 执行查询
            cursor.execute(query_sql, (reference_number,))
            sku_results = cursor.fetchall()

            if sku_results:
                for sku_record in sku_results:
                    # 添加订单号信息到结果中
                    sku_record['order_code'] = reference_number
                return sku_results
            else:
                # TODO 钉钉提醒
                return []

        except Error as e:
            logger.error(f"查询订单 {reference_number} SKU信息出错: {e}")
            return []
        finally:
            if connection:
                cursor.close()
                connection.close()

    def update_order_status(reference_number, new_status, path=None, file_name=None, upload_time=None,
                            request_response=None):
        """
        根据reference_number更新订单状态及相关信息

        Args:
            reference_number: 订单参考号
            new_status: 新状态 ('uploaded', 'upload_failed')
            path: 文件路径
            file_name: 文件名
            upload_time: 上传时间
            request_response: 请求响应
        """
        connection = None
        try:
            # 建立数据库连接
            connection = pymysql.connect(**DB_CONFIG)
            cursor = connection.cursor()

            # 构建动态SQL
            fields_to_update = ['status = %s', 'update_datetime = %s']
            values = [new_status, datetime.now().strftime('%Y-%m-%d %H:%M:%S')]

            # 根据传入的参数动态添加字段
            if path is not None:
                fields_to_update.append('path = %s')
                values.append(path)

            if file_name is not None:
                fields_to_update.append('file_name = %s')
                values.append(file_name)

            if upload_time is not None:
                fields_to_update.append('upload_time = %s')
                values.append(upload_time)

            if request_response is not None:
                fields_to_update.append('request_response = %s')
                values.append(request_response)

            # 添加WHERE条件参数
            values.append(reference_number)

            # 构建完整的SQL语句
            update_sql = f"""
            UPDATE sgm_tracking_record 
            SET {', '.join(fields_to_update)}
            WHERE reference_number = %s
            """

            # 执行更新
            cursor.execute(update_sql, values)
            connection.commit()

            logger.info(f"订单 {reference_number} - 状态已更新为: {new_status}")
            if path:
                logger.debug(f"  路径: {path}")
            if file_name:
                logger.debug(f"  文件名: {file_name}")
            if upload_time:
                logger.debug(f"  上传时间: {upload_time}")
            return True

        except Error as e:
            logger.error(f"更新订单 {reference_number} 状态出错: {e}")
            if connection:
                connection.rollback()
            return False
        finally:
            if connection:
                cursor.close()
                connection.close()

    # 查询未上传的订单
    logger.info("开始查询未上传的订单")
    not_uploaded_orders = query_not_uploaded_orders()

    # 如果有未上传的订单，查询它们的SKU信息
    if not_uploaded_orders:
        logger.info(f"查询到 {len(not_uploaded_orders)} 个未上传的订单，开始处理...")

        all_sku_results = []
        for order in not_uploaded_orders:
            # 参考号
            reference_number = order['reference_number']
            # 跟踪号
            tracking_number = order['tracking_number']

            sku_results = query_order_sku_by_reference(reference_number)

            # 如果sku_results为空列表，跳过处理
            if sku_results == []:
                # 调用API获取SKU信息
                product_sku_org = query_order_sku_by_reference_api(reference_number, ec_cookie)
                # 如果API调用失败返回None，跳过当前订单
                if product_sku_org is None:
                    logger.warning(f"订单号: {reference_number} - API调用失败，跳过处理")
                    continue
            else:
                product_sku_org = sku_results[0]['product_sku_org']

            if not product_sku_org:
                logger.warning(f"订单号: {reference_number} - 未找到SKU信息，跳过处理")
                continue

            # 拼接文件路径
            data_path = f'\\\\segmart-nas\\物流相关资料-公开\\透明计划\\{product_sku_org}'

            # 文件夹不存在
            if not (os.path.exists(data_path) and os.path.isdir(data_path)):
                logger.warning(f"订单 {reference_number} - 文件夹不存在: {data_path}")
                # 这里可以添加后续处理逻辑
                continue

            # 文件夹存在，获取路径下所有文件夹的所有文件
            logger.info(f"订单 {reference_number} - 开始获取文件夹下的所有文件: {data_path}")

            all_files = []
            try:
                # 使用os.walk递归遍历所有子目录
                for root, dirs, files in os.walk(data_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        all_files.append(file_path)

                def extract_sequence_number(file_path):
                    """提取文件名中的序号用于排序"""
                    try:
                        filename = os.path.basename(file_path)
                        # 去除文件扩展名
                        name_without_ext = os.path.splitext(filename)[0]
                        # 直接将文件名转换为数字
                        return int(name_without_ext)
                    except ValueError:
                        # 如果无法转换为数字，放在最后
                        return float('inf')

                # 对文件列表进行排序
                all_files_sorted = sorted(all_files, key=extract_sequence_number)

                # 判断是否存在文件
                if not all_files_sorted or all_files_sorted == []:
                    logger.warning(f"订单 {reference_number} - 文件夹下没有文件: {data_path}")
                    continue

                unassigned_jpg_files = [
                    file for file in all_files_sorted
                    if file.lower().endswith(".jpg")
                ]

                if not unassigned_jpg_files:
                    logger.warning(f"订单 {reference_number} - 没有找到未分配的 .jpg 文件")
                    # 更新数据库中的订单状态为上传失败
                    continue

                try:
                    original_file = Path(unassigned_jpg_files[0])  # 转为 Path 对象
                    new_filepath = original_file.with_stem(f"{tracking_number}")  # 修改文件名
                    # 重命名文件
                    original_file.rename(new_filepath)
                    logger.info(f"订单 {reference_number} - 文件已重命名: {new_filepath}")
                    result = upload_file(new_filepath, fileToken)
                    if not result:
                        raise Exception("获取UUID失败")
                    findLabelFileUploadInfo(result, original_file, infoToken)
                    # 更新数据库中的订单状态为已上传
                    update_order_status(reference_number, 'uploaded')

                    # 找到文件列表中的最大数字
                    max_num = 0
                    for file_path in all_files_sorted:
                        try:
                            filename = os.path.basename(file_path)
                            name_without_ext = os.path.splitext(filename)[0]
                            num = int(name_without_ext)
                            if num > max_num:
                                max_num = num
                        except ValueError:
                            continue

                    # 将文件再次重命名为最大数字加1
                    newest_filepath = new_filepath.with_stem(f"{max_num + 1}")
                    Path(new_filepath).rename(newest_filepath)
                    logger.info(f"订单 {reference_number} - 文件已重命名为最大数字+1: {newest_filepath}")
                except Exception as rename_error:
                    logger.error(f"订单 {reference_number} - 文件重命名失败: {rename_error}")
                    # 更新数据库中的订单状态为上传失败
                    update_order_status(reference_number, 'upload_failed')
            except Exception as e:
                logger.error(f"订单 {reference_number} - 遍历文件夹出错: {e}")
                # 更新数据库中的订单状态为上传失败
                update_order_status(reference_number, 'upload_failed')
    else:
        logger.info("没有未上传的订单需要处理")

    logger.info("定时任务执行完成")


def run_scheduler():
    """运行定时任务调度器"""
    logger.info("启动定时任务调度器")
    logger.info("定时任务已设置 - 每分钟执行一次")

    try:
        logger.info("定时任务调度器开始运行...")

        while not stop_flag.is_set():
            try:
                # 执行定时任务
                main_data()

                # 等待60秒，但可以被停止信号中断
                for i in range(120):
                    if stop_flag.is_set():
                        break
                    time.sleep(1)

            except Exception as e:
                logger.error(f"执行定时任务时出现错误: {e}")
                # 发生错误时等待30秒再继续
                for i in range(30):
                    if stop_flag.is_set():
                        break
                    time.sleep(1)

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号...")
    except Exception as e:
        logger.error(f"调度器运行时出现意外错误: {e}")
    finally:
        logger.info("定时任务调度器已停止")


if __name__ == "__main__":
    run_scheduler()